<?php

namespace Webkul\Admin\Http\Controllers\Sales;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Event;
use Webkul\Admin\DataGrids\Sales\OrderInvoiceDataGrid;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Core\Traits\PDFHandler;
use Webkul\Sales\Repositories\InvoiceRepository;
use Webkul\Sales\Repositories\OrderRepository;
use Webkul\Checkout\Repositories\CartComplementaryProductRepository;
use Webkul\Product\Repositories\ComplementaryProductRepository;

class InvoiceController extends Controller
{
    use PDFHandler;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected OrderRepository $orderRepository,
        protected InvoiceRepository $invoiceRepository,
        protected CartComplementaryProductRepository $cartComplementaryProductRepository,
        protected ComplementaryProductRepository $complementaryProductRepository
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        if (request()->ajax()) {
            return datagrid(OrderInvoiceDataGrid::class)->process();
        }

        return view('admin::sales.invoices.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(int $orderId)
    {
        $order = $this->orderRepository->findOrFail($orderId);

        if ($order->payment->method === 'paypal_standard') {
            abort(404);
        }

        return view('admin::sales.invoices.create', compact('order'));
    }

    /**
     * (Store) a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(int $orderId)
    {
        $order = $this->orderRepository->findOrFail($orderId);

        if (! $order->canInvoice()) {
            session()->flash('error', trans('admin::app.sales.invoices.create.creation-error'));

            return redirect()->back();
        }

        $this->validate(request(), [
            'invoice.items'   => 'required|array',
            'invoice.items.*' => 'required|numeric|min:0',
        ]);

        if (! $this->invoiceRepository->haveProductToInvoice(request()->all())) {
            session()->flash('error', trans('admin::app.sales.invoices.create.product-error'));

            return redirect()->back();
        }

        if (! $this->invoiceRepository->isValidQuantity(request()->all())) {
            session()->flash('error', trans('admin::app.sales.invoices.create.invalid-qty'));

            return redirect()->back();
        }

        $this->invoiceRepository->create(array_merge(request()->all(), [
            'order_id' => $orderId,
        ]));

        session()->flash('success', trans('admin::app.sales.invoices.create.create-success'));

        return redirect()->route('admin.sales.orders.view', $orderId);
    }

    /**
     * Show the view for the specified resource.
     *
     * @return \Illuminate\View\View
     */
    public function view(int $id)
    {
        $invoice = $this->invoiceRepository->findOrFail($id);

        $complementaryProducts = $this->getComplementaryProducts($invoice);

        return view('admin::sales.invoices.view', compact('invoice', 'complementaryProducts'));
    }

    /**
     * Send duplicate invoice.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendDuplicateEmail(Request $request, int $id)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $invoice = $this->invoiceRepository->findOrFail($id);

        $invoice->email = request()->input('email');

        Event::dispatch('sales.invoice.send_duplicate_email', $invoice);

        session()->flash('success', trans('admin::app.sales.invoices.view.invoice-sent'));

        return redirect()->route('admin.sales.invoices.view', $invoice->id);
    }

    /**
     * Print and download the for the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function printInvoice(int $id)
    {
        $invoice = $this->invoiceRepository->findOrFail($id);

        $complementaryProducts = [];

        if ($invoice->order->cart_id) {
            $cartComplementaryProductIds = $this->cartComplementaryProductRepository
                ->where('cart_id', $invoice->order->cart_id)
                ->pluck('complementary_product_id');

            if ($cartComplementaryProductIds->isNotEmpty()) {
                $complementaryProducts = $this->complementaryProductRepository
                    ->whereIn('id', $cartComplementaryProductIds)
                    ->get();
            }

            // Get all possible sizes
            $sizes = ['2 ml', '10 ml'];

            // Initialize empty array with default values for all sizes
            $defaultProducts = array_fill_keys($sizes, [
                'key' => '0pcs',
                'value' => ''
            ]);

            // Group products by size and build dynamic data
            $groupedProducts = $complementaryProducts->groupBy('size')->map(function ($products) {
                $count = $products->count();
                $size = trim($products->first()->size);

                return [
                    'key' => "{$count}pcs",
                    'value' => $products->pluck('name')->implode(', ')
                ];
            })->toArray();

            // Merge default and actual products, with size as part of value rather than key
            $complementaryProducts = array_map(function($size) use ($groupedProducts) {
                $sizeKey = trim($size);
                return array_merge([
                    'size' => $sizeKey,
                    'key' => isset($groupedProducts[$sizeKey]) ? $groupedProducts[$sizeKey]['key'] : '0pcs',
                    'value' => isset($groupedProducts[$sizeKey]) ? $groupedProducts[$sizeKey]['value'] : ''
                ]);
            }, $sizes);
        }

        return $this->downloadPDF(
            view('admin::sales.invoices.pdf', compact('invoice', 'complementaryProducts'))->render(),
            'invoice-'.$invoice->created_at->format('d-m-Y')
        );
    }

    public function getComplementaryProducts($data)
    {
        $complementaryProducts = [];

        if ($data->order->cart_id) {
            $cartComplementaryProductIds = $this->cartComplementaryProductRepository
                ->where('cart_id', $data->order->cart_id)
                ->pluck('complementary_product_id');

            if ($cartComplementaryProductIds->isNotEmpty()) {
                $complementaryProducts = $this->complementaryProductRepository
                    ->whereIn('id', $cartComplementaryProductIds)
                    ->get();
            }

        }

        return $complementaryProducts;
    }
}
