<?php

namespace Webkul\Admin\Http\Controllers\Settings;

use Illuminate\Http\JsonResponse;
use Webkul\Admin\DataGrids\Settings\CountriesDataGrid;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Core\Repositories\CountryRepository;

class CountryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(protected CountryRepository $countryRepository) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        if (request()->ajax()) {
            return datagrid(CountriesDataGrid::class)->process();
        }

        return view('admin::settings.countries.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(): JsonResponse
    {
        $this->validate(request(), [
            'code'        => ['required', 'unique:countries,code', new \Webkul\Core\Rules\Code],
            'name'        => 'required',
            'status'      => 'boolean',
            'currency_id' => 'nullable|exists:currencies,id',
            'locale_id'   => 'nullable|exists:locales,id',
        ]);

        $this->countryRepository->create(request()->only([
            'code',
            'name',
            'status',
            'currency_id',
            'locale_id',
        ]));

        return new JsonResponse([
            'message' => trans('admin::app.settings.countries.index.create-success'),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id): JsonResponse
    {
        $country = $this->countryRepository->findOrFail($id);

        return new JsonResponse([
            'data' => $country,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(): JsonResponse
    {
        $this->validate(request(), [
            'name'        => 'required',
            'status'      => 'boolean',
            'currency_id' => 'nullable',
            'locale_id'   => 'nullable',
        ]);

        $data = request()->all();

        $data['currency_id'] = $data['currency_id'] ?: null;
        $data['locale_id'] = $data['locale_id'] ?: null;

        $this->countryRepository->update($data, request()->id);

        return new JsonResponse([
            'message' => trans('admin::app.settings.countries.index.update-success'),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $country = $this->countryRepository->findOrFail($id);

        if ($country->count() == 1) {
            return response()->json([
                'message' => trans('admin::app.settings.countries.index.last-delete-error'),
            ], 400);
        }

        try {
            $country->delete($id);

            return new JsonResponse([
                'message' => trans('admin::app.settings.countries.index.delete-success'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => trans('admin::app.settings.countries.index.delete-failed'),
            ], 500);
        }
    }
}
