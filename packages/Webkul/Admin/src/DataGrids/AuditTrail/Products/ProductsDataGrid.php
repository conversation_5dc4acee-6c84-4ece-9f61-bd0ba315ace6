<?php

namespace Webkul\Admin\DataGrids\AuditTrail\Products;

use Illuminate\Support\Facades\DB;
use Webkul\DataGrid\DataGrid;

class ProductsDataGrid extends DataGrid
{
    protected $primaryColumn = 'id';

    public function prepareQueryBuilder()
    {
        $queryBuilder = DB::table('activity_log')
            ->select('id', 'log_name', 'event', 'subject_id', 'causer_id', 'properties', 'created_at')
            ->where('log_name', 'products-log');

        return $queryBuilder;
    }

    public function prepareColumns()
    {
        $this->addColumn([
            'index' => 'id',
            'label' => trans('admin::app.audit-trail.datagrid.id'),
            'type' => 'integer',
            'searchable' => true,
            'sortable' => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index' => 'log_name',
            'label' => trans('admin::app.audit-trail.datagrid.log-name'),
            'type' => 'string',
            'searchable' => true,
            'sortable' => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index' => 'event',
            'label' => trans('admin::app.audit-trail.datagrid.event'),
            'type' => 'string',
            'searchable' => true,
            'sortable' => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index' => 'subject_id',
            'label' => trans('admin::app.audit-trail.datagrid.product-id'),
            'type' => 'string',
            'searchable' => true,
            'sortable' => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index' => 'causer_id',
            'label' => trans('admin::app.audit-trail.datagrid.user-id'),
            'type' => 'string',
            'searchable' => true,
            'sortable' => true,
            'filterable' => true,
        ]);

        $this->addColumn([
            'index' => 'properties',
            'label' => trans('admin::app.audit-trail.datagrid.properties'),
            'type' => 'string',
            'searchable' => false,
            'sortable' => false,
            'filterable' => false,
        ]);

        $this->addColumn([
            'index' => 'created_at',
            'label' => trans('admin::app.audit-trail.datagrid.created-at'),
            'type' => 'datetime',
            'searchable' => true,
            'sortable' => true,
            'filterable' => true,
        ]);
    }

    public function prepareActions()
    {
        if(bouncer()->hasPermission('audit-trail.products.view')) {
            $this->addAction([
                'icon' => 'icon-view',
                'title' => trans('admin::app.audit-trail.datagrid.view'),
                'method' => 'GET',
                'url' => function ($row) {
                    return route('admin.audit-trail.products.view', $row->id);
                },
            ]);
        }
    }
}