<x-admin::layouts>
    <x-slot:title>
        @lang('admin::app.settings.countries.index.title')
    </x-slot>

    {!! view_render_event('bagisto.admin.settings.countries.create.before') !!}

    <v-countries>
        <div class="page-title flex items-center justify-between gap-4 max-sm:flex-wrap">
            <p class="text-xl font-bold text-gray-800 dark:text-white">
                @lang('admin::app.settings.countries.index.title')
            </p>

            <div class="flex items-center gap-x-2.5">
                @if (bouncer()->hasPermission('settings.countries.create'))
                    <button
                        type="button"
                        class="primary-button"
                    >
                        @lang('admin::app.settings.countries.index.create-btn')
                    </button>
                @endif
            </div>
        </div>

        <!-- DataGrid Shimmer -->
        <x-admin::shimmer.datagrid />
    </v-countries>

    {!! view_render_event('bagisto.admin.settings.countries.create.after') !!}

    @pushOnce('scripts')
        <script
            type="text/x-template"
            id="v-countries-template"
        >
            <div class="page-title flex items-center justify-between gap-4 max-sm:flex-wrap">
                <p class="text-xl font-bold text-gray-800 dark:text-white">
                    @lang('admin::app.settings.countries.index.title')
                </p>

                <div class="flex items-center gap-x-2.5">
                    <!-- Country Create Button -->
                    @if (bouncer()->hasPermission('settings.countries.create'))
                        <button
                            type="button"
                            class="primary-button"
                            @click="selectedCountries=0;resetForm();$refs.countryUpdateOrCreateModal.toggle()"
                        >
                            @lang('admin::app.settings.countries.index.create-btn')
                        </button>
                    @endif
                </div>
            </div>

            <x-admin::datagrid
                :src="route('admin.settings.countries.index')"
                ref="datagrid"
            >
                <!-- DataGrid Body -->
                <template #body="{
                    isLoading,
                    available,
                    applied,
                    selectAll,
                    sort,
                    performAction
                }">
                    <template v-if="isLoading">
                        <x-admin::shimmer.datagrid.table.body />
                    </template>

                    <template v-else>
                        <div
                            v-for="record in available.records"
                            class="table-row row grid items-center gap-2.5 border-b px-4 py-4 text-gray-600 transition-all hover:bg-gray-50 dark:border-gray-800 dark:text-gray-300 dark:hover:bg-gray-950"
                            :style="`grid-template-columns: repeat(${gridsCount}, minmax(0, 1fr))`"
                        >
                            <!-- ID -->
                            <p>@{{ record.id }}</p>

                            <!-- Code -->
                            <p>@{{ record.code }}</p>

                            <!-- Name -->
                            <p>@{{ record.name }}</p>

                            <!-- Status -->
                            <p>@{{ record.status }}</p>

                            <!-- Actions -->
                            <div class="flex justify-end">
                                @if (bouncer()->hasPermission('settings.countries.edit'))
                                    <a @click="selectedCountries=1; editModal(record.actions.find(action => action.index === 'edit')?.url)">
                                        <span
                                            :class="record.actions.find(action => action.index === 'edit')?.icon"
                                            class="cursor-pointer rounded-md p-1.5 text-2xl transition-all hover:bg-widian-dark dark:hover:bg-gray-800 max-sm:place-self-center"
                                        >
                                        </span>
                                    </a>
                                @endif

                                @if (bouncer()->hasPermission('settings.countries.delete'))
                                    <a @click="performAction(record.actions.find(action => action.index === 'delete'))">
                                        <span
                                            :class="record.actions.find(action => action.index === 'delete')?.icon"
                                            class="cursor-pointer rounded-md p-1.5 text-2xl transition-all hover:bg-widian-dark dark:hover:bg-gray-800 max-sm:place-self-center"
                                        >
                                        </span>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </template>
                </template>
            </x-admin::datagrid>

            <x-admin::form
                v-slot="{ meta, errors, handleSubmit }"
                as="div"
                ref="modalForm"
            >
                <form
                    @submit="handleSubmit($event, updateOrCreate)"
                    ref="createCountryForm"
                >

                    {!! view_render_event('bagisto.admin.settings.countries.create_form_controls.before') !!}

                    <x-admin::modal ref="countryUpdateOrCreateModal">
                        <!-- Modal Header -->
                        <x-slot:header>
                            <p class="text-lg font-bold text-gray-800 dark:text-white">
                                <span v-if="selectedLocales">
                                    @lang('admin::app.settings.countries.index.edit.title')
                                </span>

                                <span v-else>
                                    @lang('admin::app.settings.countries.index.create.title')
                                </span>
                            </p>
                        </x-slot>

                        <!-- Modal Content -->
                        <x-slot:content>
                            {!! view_render_event('bagisto.admin.settings.countries.create.before') !!}

                            <x-admin::form.control-group.control
                                type="hidden"
                                name="id"
                                v-model="country.id"
                            />

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.settings.countries.index.create.code')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="text"
                                    id="code"
                                    name="code"
                                    rules="required"
                                    v-model="country.code"
                                    :label="trans('admin::app.settings.countries.index.create.code')"
                                    :placeholder="trans('admin::app.settings.countries.index.create.code')"
                                    ::disabled="country.id"
                                />

                                <x-admin::form.control-group.error control-name="code" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.settings.countries.index.create.name')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="text"
                                    id="name"
                                    name="name"
                                    rules="required"
                                    v-model="country.name"
                                    :label="trans('admin::app.settings.countries.index.create.name')"
                                    :placeholder="trans('admin::app.settings.countries.index.create.name')"
                                />

                                <x-admin::form.control-group.error control-name="name" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    @lang('admin::app.settings.countries.index.create.currency')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="select"
                                    name="currency_id"
                                    v-model="country.currency_id"
                                    :label="trans('admin::app.settings.countries.index.create.currency')"
                                >
                                    <option value="">@lang('admin::app.settings.countries.index.create.select')</option>

                                    <option
                                        v-for="currency in currencies"
                                        :value="currency.id"
                                        :text="currency.name"
                                        :selected="currency.id == country.currency_id"
                                    >
                                    </option>
                                </x-admin::form.control-group.control>

                                <x-admin::form.control-group.error control-name="currency_id" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    @lang('admin::app.settings.countries.index.create.locale')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="select"
                                    name="locale_id"
                                    v-model="country.locale_id"
                                    :label="trans('admin::app.settings.countries.index.create.locale')"
                                >
                                    <option value="">@lang('admin::app.settings.taxes.categories.index.create.select')</option>

                                    <option
                                        v-for="locale in locales"
                                        :value="locale.id"
                                        :text="locale.name"
                                        :selected="locale.id == country.locale_id"
                                    >
                                    </option>
                                </x-admin::form.control-group.control>

                                <x-admin::form.control-group.error control-name="locale_id" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    @lang('admin::app.settings.countries.index.create.status')
                                </x-admin::form.control-group.label>

                                <input type="hidden" name="status" value="0">

                                <x-admin::form.control-group.control
                                    type="switch"
                                    name="status"
                                    v-model="country.status"
                                    value="1"
                                    :label="trans('admin::app.settings.countries.index.create.status')"
                                    :placeholder="trans('admin::app.settings.countries.index.create.status')"
                                    ::checked="country.status == 1"
                                />

                                <x-admin::form.control-group.error control-name="status" />
                            </x-admin::form.control-group>

                            {!! view_render_event('bagisto.admin.settings.countries.create.after') !!}
                        </x-slot>

                        <!-- Modal Footer -->
                        <x-slot:footer>
                            <div class="flex items-center gap-x-2.5">
                                <button
                                    type="submit"
                                    class="primary-button"
                                >
                                    @lang('admin::app.settings.countries.index.create.save-btn')
                                </button>
                            </div>
                        </x-slot>
                    </x-admin::modal>

                    {!! view_render_event('bagisto.admin.settings.countries.create_form_controls.after') !!}

                </form>
            </x-admin::form>
        </script>

        <script type="module">
            app.component('v-countries', {
                template: '#v-countries-template',

                data() {
                    return {
                        country: {
                            code: '',
                            name: '',
                            status: 0,
                            currency_id: null,
                            locale_id: null,
                        },

                        currencies: [],
                        locales: [],

                        selectedCountries: 0,
                    }
                },

                computed: {
                    gridsCount() {
                        let count = this.$refs.datagrid.available.columns.length;

                        if (this.$refs.datagrid.available.actions.length) {
                            ++count;
                        }

                        if (this.$refs.datagrid.available.massActions.length) {
                            ++count;
                        }

                        return count;
                    },
                },

                mounted() {
                    this.getCurrencies();
                    this.getLocales();
                },

                methods: {
                    updateOrCreate(params, { resetForm, setErrors  }) {
                        let formData = new FormData(this.$refs.createCountryForm);

                        if (params.id) {
                            formData.append('_method', 'put');
                        }

                        this.$axios.post(params.id ? "{{ route('admin.settings.countries.update') }}" : "{{ route('admin.settings.countries.store') }}", formData)
                        .then((response) => {
                            this.$refs.countryUpdateOrCreateModal.close();

                            this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                            this.$refs.datagrid.get();

                            resetForm();
                        })
                        .catch(error => {
                            if (error.response.status == 422) {
                                setErrors(error.response.data.errors);
                            }
                        });
                    },

                    editModal(url) {
                        this.$axios.get(url)
                            .then((response) => {
                                this.country = {
                                    ...response.data.data,
                                };

                                this.$refs.countryUpdateOrCreateModal.toggle();
                            })
                    },

                    resetForm() {
                        this.country = {
                            code: '',
                            name: '',
                            status: 0,
                            currency_id: null,
                            locale_id: null,
                        };
                    },

                    getCurrencies() {
                        this.$axios.get("{{ route('admin.settings.currencies.index') }}")
                            .then((response) => {
                                this.currencies = response.data.records;
                            });
                    },

                    getLocales() {
                        this.$axios.get("{{ route('admin.settings.locales.index') }}")
                            .then((response) => {
                                this.locales = response.data.records;
                            });
                    },
                },
            });
        </script>
    @endPushOnce
</x-admin::layouts>
