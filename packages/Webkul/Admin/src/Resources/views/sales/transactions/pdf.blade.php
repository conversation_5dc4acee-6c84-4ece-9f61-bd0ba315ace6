<!DOCTYPE html>
<html>

<head>
    <title>@lang('admin::app.sales.transactions.receipt-pdf.title')</title>
    <style>
        @page {
            size: A5;
            margin: 2mm;
        }

        body {
            font-family: "Century Gothic", sans-serif;
            margin: 0;
            padding: 0;
            color: #030000;
        }
    </style>
</head>

<body style="font-family: 'Century Gothic', sans-serif; font-size: 12pt; color: #030000; padding: 20px; max-width: 148mm; margin: auto;">

    <!-- Main Container with White Background -->
    <div style="background-color: #FFFFFF; padding: 60px 20px; max-width: 600px; margin: auto;">

        <!-- Success Icon and Message -->
        <div style="text-align: center; margin-bottom: 40px;">
            <div>
                @if ($transaction->status == 'paid')
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('widian-assets/images/success-icon.png'))) }}">
                @else
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('widian-assets/images/pending-icon.png'))) }}">
                @endif
            </div>
            <h2 style="margin: 10px 0; font-family: 'Century Gothic', sans-serif;">{{ $transaction->status == 'paid' ? __('admin::app.sales.transactions.receipt-pdf.payment-success') : __('admin::app.sales.transactions.receipt-pdf.payment-pending') }}!</h2>
            <p style="margin: 0; font-family: 'Century Gothic', sans-serif;">{{ $transaction->status == 'paid' ? __('admin::app.sales.transactions.receipt-pdf.payment-success-description') : __('admin::app.sales.transactions.receipt-pdf.payment-pending-description') }}</p>
        </div>

        <!-- Combined Payment Info and Order Details Table -->
        <div
            style="margin-bottom: 20px; padding: 30px; background-color: #FCF9F2; border-radius: 6px; overflow: hidden; font-family: 'Century Gothic', sans-serif; font-size: 14px; color: #030000;">
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 10px 0px; text-align: left; color: #666666;">@lang('admin::app.sales.transactions.receipt-pdf.amount')</td>
                    <td style="padding: 10px 0px; text-align: right; font-size: 22px;">
                        <strong>{{ core()->formatPrice($transaction->amount) }}</strong>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 10px 0px; text-align: left; color: #666666;">@lang('admin::app.sales.transactions.receipt-pdf.payment-status')</td>
                    <td style="padding: 10px 0px; text-align: right;">
                        <span style="display: inline-flex; align-items: center; justify-content: center; background-color: {{ $transaction->status == 'paid' ? 'rgb(45 178 36 / 12%)' : 'rgb(255 193 7 / 12%)' }};color: {{ $transaction->status == 'paid' ? '#2DB224' : '#FFC107' }};padding: 5px 15px;border-radius: 50px;"><strong>{{ $transaction->status == 'paid' ? __('admin::app.sales.transactions.receipt-pdf.success') : __('admin::app.sales.transactions.receipt-pdf.pending') }}</strong></span>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="height: 20px;"></td>
                </tr>
                <tr>
                    <td colspan="2" style="height: 1px; background-color: #DCDEE0; padding: 0;"></td>
                </tr>
                <tr>
                    <td colspan="2" style="height: 20px;"></td>
                </tr>
                <tr>
                    <td style="padding: 10px 0px; text-align: left; color: #666666;">@lang('admin::app.sales.transactions.receipt-pdf.order-number')</td>
                    <td style="padding: 10px 0px; text-align: right;">#{{ $transaction->order_id }}</td>
                </tr>
                <tr>
                    <td style="padding: 10px 0px; text-align: left; color: #666666;">@lang('admin::app.sales.transactions.receipt-pdf.store-merchant-name')</td>
                    <td style="padding: 10px 0px; text-align: right;">{{ core()->getConfigData('sales.shipping.origin.store_name') }}</td>
                </tr>
                @if ($transaction->status == 'paid')
                    <tr>
                        <td style="padding: 10px 0px; text-align: left; color: #666666;">@lang('admin::app.sales.transactions.receipt-pdf.payment-method')</td>
                        <td style="padding: 10px 0px; text-align: right;">{{ Str::title($transaction->payment_method) }}</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0px; text-align: left; color: #666666;">@lang('admin::app.sales.transactions.receipt-pdf.payment-time')</td>
                        <td style="padding: 10px 0px; text-align: right;">{{ core()->formatDate($transaction->created_at, 'M d, Y, H:i:s') }}</td>
                    </tr>
                @endif
            </table>
        </div>

        <!-- Footer Section -->
        <div style="text-align: center; margin-top: 40px; font-size: 14px;">
            <table style="width: 100%;">
                <tr style="text-align: center; width: 100%;">
                    <td style="text-align: center; width: 100%;">
                        <a href="{{ config('app.url') }}" target="_blank">
                            @if (core()->getConfigData('sales.invoice_settings.pdf_print_outs.logo'))
                                <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('storage/' . core()->getConfigData('sales.invoice_settings.pdf_print_outs.logo')))) }}" alt="Logo" style="max-width: 150px; margin-bottom: 10px;"/>
                            @else
                                <img src="data:image/png;base64,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" alt="Logo" style="max-width: 150px; margin-bottom: 10px;"/>
                            @endif
                        </a>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center; width: 100%;">
                        <span style="color: #030000;">
                            @lang('admin::app.sales.transactions.receipt-pdf.have-any-troubles')
                            <a href="https://support.stripe.com" style="color: #3D3C3A; text-decoration: none;">support.stripe.com</a>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

    </div>


</body>

</html>
