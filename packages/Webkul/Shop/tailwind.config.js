/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ["./src/Resources/**/*.blade.php", "./src/Resources/**/*.js"],

    theme: {
        container: {
            center: true,

            screens: {
                "2xl": "1440px",
            },

            padding: {
                DEFAULT: "90px",
            },
        },

        screens: {
            sm: "525px",
            md: "768px",
            lg: "1024px",
            xl: "1240px",
            "2xl": "1440px",
            1180: "1180px",
            1060: "1060px",
            991: "991px",
            868: "868px",
        },

        extend: {
            colors: {
                navyBlue: "#060C3B",
                lightOrange: "#F6F2EB",
                darkGreen: '#40994A',
                darkBlue: '#0044F2',
                darkPink: '#F85156',
                primary: "#FDF9F2",
                "primary-dark": "#EFECE2",
                secondary: "#757C5B",
                "dark-gray": "#3D3C3A",
                "dark-gray-2": "#666666",
                "light-gray": "#CCCCCC",
                "light-gray-2": "#707070",
                "widian-red": "#AE000C",
                black: "#030000",
            },

            fontFamily: {
                sans: ['"Century Gothic"', "sans-serif"],
                playfair: ['"Playfair Display"', "sans-serif"],
                helvetica: ["Helvetica", "Arial", "sans-serif"],
                poppins: ["Poppins"],
                dmserif: ["DM Serif Display"],
                dubaiRegular: ["dubai-regular"],
                dubaiBold: ["dubai-bold"],
                dubaiMedium: ["dubai-medium"],
                dubaiLight: ["dubai-light"],
                gehiliBook: ['gehili-book'],
                gehiliLight: ['gehili-light'],
            },
        }
    },

    plugins: [],

    safelist: [
        {
            pattern: /icon-/,
        },
        "bg-primary-dark"
    ]
};
