<?php

namespace Webkul\Shop\Http\Controllers\API;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Event;
use Webkul\Shop\Http\Requests\Customer\LoginRequest;
use Webkul\Shop\Http\Requests\Customer\SendOtpRequest;
use Webkul\Shop\Mail\Customer\sendOtpNotification;
use Webkul\Customer\Repositories\CustomerRepository;

class CustomerController extends APIController
{
    public $customerRepository;

    public function __construct(CustomerRepository $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * Login Customer
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login($locale, LoginRequest $request)
    {
        $isLoginFromCheckout = $request->isLoginFromCheckout ?? false;

        if($request->loginWithPassword) {
            if (! auth()->guard('customer')->attempt($request->only(['email', 'password']))) {
                return response()->json([
                    'message' => trans('shop::app.customers.login-form.invalid-credentials'),
                ], Response::HTTP_FORBIDDEN);
            }
        } else {
            $customer = $this->customerRepository->where('email', $request->email)->first();

            $otp = $customer->otp;
            $expiresAt = $customer->otp_expires_at;

            if ($otp != $request->otp) {
                return response()->json([
                    'message' => trans('shop::app.customers.login-form.invalid-otp'),
                ], Response::HTTP_FORBIDDEN);
            } else {
                if (now() > $expiresAt) {
                    return response()->json([
                        'message' => trans('shop::app.customers.login-form.invalid-otp'),
                    ], Response::HTTP_FORBIDDEN);
                }
            }

            auth()->guard('customer')->login($customer);
        }

        if (! auth()->guard('customer')->user()->status) {
            auth()->guard('customer')->logout();

            return response()->json([
                'message' => trans('shop::app.customers.login-form.not-activated'),
            ], Response::HTTP_FORBIDDEN);
        }

        if (! auth()->guard('customer')->user()->is_verified) {
            Cookie::queue(Cookie::make('enable-resend', 'true', 1));

            Cookie::queue(Cookie::make('email-for-resend', $request->get('email'), 1));

            auth()->guard('customer')->logout();

            return response()->json([
                'message' => trans('shop::app.customers.login-form.verify-first'),
            ], Response::HTTP_FORBIDDEN);
        }

        /**
         * Event passed to prepare cart after login.
         */
        Event::dispatch('customer.after.login', auth()->guard()->user());

        return response()->json(['redirect_url' => $isLoginFromCheckout ? route('shop.checkout.onepage.index', app()->getLocale()) : route('shop.home.index', app()->getLocale())]);
    }

    public function sendOtp($locale, SendOtpRequest $request)
    {
        $data = $request->validated();

        $customer = $this->customerRepository->where('email', $data['email'])->first();

        if (!$customer) {
            return response()->json([
                'message' => trans('shop::app.customers.send-otp.error'),
            ], Response::HTTP_FORBIDDEN);
        }

        $otp = rand(1000, 9999);
        $customer->otp = $otp;
        $customer->otp_expires_at = now()->addMinutes(5);
        $customer->save();

        try {
            Mail::queue(new sendOtpNotification($customer, $otp));

            return response()->json([
                'message' => trans('shop::app.customers.send-otp.success'),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json([
                'message' => trans('shop::app.customers.send-otp.error'),
                'errors' => $e->getMessage(),
            ], Response::HTTP_FORBIDDEN);
        }
    }
}
