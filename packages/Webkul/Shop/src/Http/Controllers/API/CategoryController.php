<?php

namespace Webkul\Shop\Http\Controllers\API;

use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Category\Repositories\CategoryRepository;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Shop\Http\Resources\AttributeResource;
use Webkul\Shop\Http\Resources\CategoryResource;
use Webkul\Shop\Http\Resources\CategoryTreeResource;
use Webkul\Theme\Repositories\ThemeCustomizationRepository;
use Webkul\Shop\Http\Resources\ProductResource;

class CategoryController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected AttributeRepository $attributeRepository,
        protected CategoryRepository $categoryRepository,
        protected ProductRepository $productRepository,
        protected ThemeCustomizationRepository $themeCustomizationRepository,
    ) {}

    /**
     * Get all categories.
     */
    public function index($locale): JsonResource
    {
        /**
         * These are the default parameters. By default, only the enabled category
         * will be shown in the current locale.
         */
        $defaultParams = [
            'status' => 1,
            'locale' => app()->getLocale(),
        ];

        $categories = $this->categoryRepository->getAll(array_merge($defaultParams, request()->all()));

        return CategoryResource::collection($categories);
    }

    /**
     * Get all categories in tree format.
     */
    public function tree($locale): JsonResource
    {
        $categories = $this->categoryRepository->getVisibleCategoryTree(core()->getCurrentChannel()->root_category_id);

        return CategoryTreeResource::collection($categories);
    }

    /**
     * Get filterable attributes for category.
     */
    public function getAttributes($locale): JsonResource
    {
        if (! request('category_id')) {
            $filterableAttributes = $this->attributeRepository->getFilterableAttributes();

            return AttributeResource::collection($filterableAttributes);
        }

        $category = $this->categoryRepository->findOrFail(request('category_id'));

        if (empty($filterableAttributes = $category->filterableAttributes)) {
            $filterableAttributes = $this->attributeRepository->getFilterableAttributes();
        }

        return AttributeResource::collection($filterableAttributes);
    }

    /**
     * Get product maximum price.
     */
    public function getProductMaxPrice($locale, $categoryId = null): JsonResource
    {
        $maxPrice = $this->productRepository->getMaxPrice(['category_id' => $categoryId]);

        return new JsonResource([
            'max_price' => core()->convertPrice($maxPrice),
        ]);
    }

    /**
     * Get all menu categories.
     */
    public function getMenuCategories($locale): JsonResource
    {
        $customizations = $this->themeCustomizationRepository->orderBy('sort_order')->findWhere([
            'status'     => 1,
            'name'     => 'Menu Categories',
            'channel_id' => core()->getCurrentChannel()->id,
            'theme_code' => core()->getCurrentChannel()->theme,
        ]);

        // Get the visible category tree
        $collections = $this->categoryRepository->getVisibleCategoryTree(null, 0);

        $filterCollections = [
            'luxury' => [],
            'prestige' => [],
        ];

        foreach ($collections as $collection) {
            if ($collection->is_luxury) {
                $filterCollections['luxury'][] = [
                    'title' => $collection->name,
                    'url' => route('shop.collection.page', [app()->getLocale(), $collection->slug]),
                ];
            }

            if ($collection->is_prestige) {
                $filterCollections['prestige'][] = [
                    'title' => $collection->name,
                    'url' => route('shop.collection.page', [app()->getLocale(), $collection->slug]),
                ];
            }
        }

        $categories = [];

        if (empty($customizations) || !isset($customizations[0]['options'])) {
            return new JsonResource([
                'data' => $categories,
            ]);
        }

        $columnMappings = [
            'column_1' => 'luxury',
            'column_2' => 'prestige',
        ];

        foreach ($customizations[0]['options'] as $key => $items) {
            if ($key === 'column_4') {
                foreach ($items as $item) {
                    $categories[] = [
                        'title' => $item['title'] ?? '',
                        'url' => $item['url'] ?? '',
                        'sort_order' => $item['sort_order'] ?? 0,
                    ];
                }
                continue;
            }

            if (isset($columnMappings[$key])) {
                $categories[] = [
                    'title' => $items[0]['title'] ?? '',
                    'url' => 'javascript:void(0)',
                    'sort_order' => $items[0]['sort_order'] ?? 0,
                    'sub_menus' => $filterCollections[$columnMappings[$key]],
                ];
            }
        }

        return new JsonResource([
            'data' => $categories,
        ]);
    }

    public function youMayAlsoLikeProducts($locale, $categoryId): JsonResource
    {
        $otherCategories = $this->categoryRepository->findWhere([
            ['id', '!=', $categoryId],
            'status' => 1
        ], ['*'], 4);

        $relatedProducts = collect();

        foreach ($otherCategories as $otherCategory) {
            $relatedProducts = $relatedProducts->merge(
                $otherCategory->products()->limit(4)->get()
            );
        }

        $relatedProducts = $relatedProducts->shuffle()->take(4);

        return ProductResource::collection($relatedProducts);
    }
}
