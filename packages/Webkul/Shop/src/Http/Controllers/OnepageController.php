<?php

namespace Webkul\Shop\Http\Controllers;

use Illuminate\Support\Facades\Event;
use Webkul\Checkout\Facades\Cart;
use Webkul\MagicAI\Facades\MagicAI;
use Webkul\Sales\Transformers\OrderResource;
use Webkul\Sales\Repositories\OrderRepository;
use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Core\Repositories\SubscribersListRepository;
use Webkul\Admin\Repositories\HealthCheckLogRepository;
use Webkul\Admin\Models\HealthCheckLog;

class OnepageController extends Controller
{
    public function __construct(
        protected OrderRepository $orderRepository,
        protected CustomerRepository $customerRepository,
        protected SubscribersListRepository $subscriptionRepository,
        protected HealthCheckLogRepository $healthCheckLogRepository,
    ) {}
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index($locale)
    {
        if (! core()->getConfigData('sales.checkout.shopping_cart.cart_page')) {
            abort(404);
        }

        Event::dispatch('checkout.load.index');

        /**
         * If guest checkout is not allowed then redirect back to the cart page
         */
        // if (
        //     ! auth()->guard('customer')->check()
        //     && ! core()->getConfigData('sales.checkout.shopping_cart.allow_guest_checkout')
        // ) {
        //     return redirect(route('shop.customer.session.index', app()->getLocale()));
        // }

        /**
         * If user is suspended then redirect back to the cart page
         */
        if (auth()->guard('customer')->user()?->is_suspended) {
            session()->flash('warning', trans('shop::app.checkout.cart.suspended-account-message'));

            return redirect()->route('shop.checkout.cart.index', app()->getLocale());
        }

        /**
         * If cart has errors then redirect back to the cart page
         */
        if (Cart::hasError()) {
            return redirect()->route('shop.checkout.cart.index', app()->getLocale());
        }

        $cart = Cart::getCart();

        /**
         * If cart is has downloadable items and customer is not logged in
         * then redirect back to the cart page
         */
        if (
            ! auth()->guard('customer')->check()
            && (
                $cart->hasDownloadableItems()
                || ! $cart->hasGuestCheckoutItems()
            )
        ) {
            return redirect()->route('shop.customer.session.index', app()->getLocale());
        }

        return view('shop::checkout.onepage.index', compact('cart'));
    }

    public function paymentComplete($locale)
    {
        if (Cart::hasError()) {
            return redirect()->route('shop.checkout.cart.index', app()->getLocale());
        }

        Cart::collectTotals();

        $cart = Cart::getCart();

        $data = (new OrderResource($cart))->jsonSerialize();

        if (session()->has('checkout_data')) {
            if (session()->has('checkout_data.is_gift') && session()->get('checkout_data.is_gift')) {
                $data['is_gift'] = 1;
            }

            if (session()->has('checkout_data.order_comments') && !empty(session()->get('checkout_data.order_comments'))) {
                $data['order_comments'] = session()->get('checkout_data.order_comments');
            }
        }

        $order = $this->orderRepository->create($data);
        $customer = auth()->guard('customer')->user();

        if (session()->has('checkout_data.terms_of_service') && session()->get('checkout_data.terms_of_service') && $customer && $customer->terms_conditions == 0) {
            $this->customerRepository->update([
                'terms_conditions' => 1
            ], $customer->id);
        }

        if (session()->has('checkout_data.promotion_emails') && session()->get('checkout_data.promotion_emails') && $customer) {

            if (!$customer->subscribed_to_news_letter) {
                $this->customerRepository->update([
                    'subscribed_to_news_letter' => 1
                ], $customer->id);

                $subscription = $this->subscriptionRepository->findOneByField('customer_id', $customer->id);

                if (isset($subscription) && !empty($subscription)) {
                    $this->subscriptionRepository->update([
                        'is_subscribed' => 1,
                        'email' => $customer->email,
                        'unsubscribe_reason' => null,
                        'token' => uniqid(),
                    ], $subscription->id);

                    $subscription = $this->subscriptionRepository->find($subscription->id);
                } else {
                    $subscription = $this->subscriptionRepository->create([
                        'email' => $customer->email,
                        'customer_id' => $customer->id,
                        'channel_id' => core()->getCurrentChannel()->id,
                        'is_subscribed' => 1,
                        'token' => uniqid(),
                    ]);
                }

                Event::dispatch('customer.subscription.after', $subscription);
            }
        }

        $this->createStripeLog(
            null,
            HealthCheckLog::STATUS_SUCCESS,
            [
                'success' => true,
                'message' => 'Stripe transaction successful',
                'order_id' => $order->id,
                'cart_id' => $cart->id,
                'currency' => $order->order_currency_code,
                'total_amount' => $cart->grand_total,
                'timestamp' => now()->toDateTimeString(),
            ]
        );

        Cart::deActivateCart();

        session()->put('order_id', $order->id);

        session()->forget('checkout_data');

        return redirect()->route('shop.checkout.onepage.success', app()->getLocale());
    }

    /**
     * Order success page.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function success($locale, OrderRepository $orderRepository)
    {
        if (! $order = $orderRepository->find(session('order_id'))) {
            return redirect()->route('shop.checkout.cart.index', app()->getLocale());
        }

        if (
            core()->getConfigData('general.magic_ai.settings.enabled')
            && core()->getConfigData('general.magic_ai.checkout_message.enabled')
            && ! empty(core()->getConfigData('general.magic_ai.checkout_message.prompt'))
        ) {

            try {
                $model = core()->getConfigData('general.magic_ai.checkout_message.model');

                $response = MagicAI::setModel($model)
                    ->setTemperature(0)
                    ->setPrompt($this->getCheckoutPrompt($order))
                    ->ask();

                $order->checkout_message = $response;
            } catch (\Exception $e) {
            }
        }

        session()->forget('order_id');

        return view('shop::checkout.success', compact('order'));
    }

    public function createStripeLog($endpoint, $status, $data = null, $errorMessage = null)
    {
        $this->healthCheckLogRepository->createLog(
            HealthCheckLog::SERVICE_STRIPE,
            $status,
            [
                'response' => isset($data) && !empty($data) ? json_encode($data) : null,
                'error_message' => isset($errorMessage) && !empty($errorMessage) ? $errorMessage : null,
                'api_endpoint' => $endpoint
            ]
        );
    }

    /**
     * Order success page.
     *
     * @param  \Webkul\Sales\Contracts\Order  $order
     * @return string
     */
    public function getCheckoutPrompt($order)
    {
        $prompt = core()->getConfigData('general.magic_ai.checkout_message.prompt');

        $products = '';

        foreach ($order->items as $item) {
            $products .= "Name: $item->name\n";
            $products .= "Qty: $item->qty_ordered\n";
            $products .= 'Price: '.core()->formatPrice($item->total)."\n\n";
        }

        $prompt .= "\n\nProduct Details:\n $products";

        $prompt .= "Customer Details:\n $order->customer_full_name \n\n";

        $prompt .= "Current Locale:\n ".core()->getCurrentLocale()->name."\n\n";

        $prompt .= "Store Name:\n".core()->getCurrentChannel()->name;

        return $prompt;
    }
}
