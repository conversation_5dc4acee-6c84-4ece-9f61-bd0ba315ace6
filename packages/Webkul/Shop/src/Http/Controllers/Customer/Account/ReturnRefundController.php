<?php

namespace Webkul\Shop\Http\Controllers\Customer\Account;

use Webkul\Checkout\Facades\Cart;
use Webkul\Core\Traits\PDFHandler;
use Webkul\Sales\Repositories\InvoiceRepository;
use Webkul\Sales\Repositories\OrderRepository;
use Webkul\Shop\DataGrids\ReturnRefundDataGrid;
use Webkul\Shop\Http\Controllers\Controller;
use Webkul\Checkout\Repositories\CartComplementaryProductRepository;
use Webkul\Product\Repositories\ComplementaryProductRepository;

class ReturnRefundController extends Controller
{
    use PDFHandler;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected OrderRepository $orderRepository,
        protected InvoiceRepository $invoiceRepository,
        protected CartComplementaryProductRepository $cartComplementaryProductRepository,
        protected ComplementaryProductRepository $complementaryProductRepository
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index($locale)
    {
        if (request()->ajax()) {
            return datagrid(ReturnRefundDataGrid::class)->process();
        }

        return view('shop::customers.account.return-refunds.index');
    }

    /**
     * Show the view for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function view($locale, $id)
    {
        $order = $this->orderRepository->findOneWhere([
            'customer_id' => auth()->guard('customer')->id(),
            'id'          => $id,
        ]);

        abort_if(! $order, 404);

        return view('shop::customers.account.return-refunds.view', compact('order'));
    }

    /**
     * Reorder action for the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function reorder($locale, int $id)
    {
        $order = $this->orderRepository->findOrFail($id);

        foreach ($order->items as $item) {
            try {
                Cart::addProduct($item->product, $item->additional);
            } catch (\Exception $e) {
                // do nothing
            }
        }

        return redirect(route('shop.checkout.cart.index', app()->getLocale()));
    }

    /**
     * Print and download the for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function printInvoice($locale, $id)
    {
        $invoice = $this->invoiceRepository->where('id', $id)
            ->whereHas('order', function ($query) {
                $query->where('customer_id', auth()->guard('customer')->id());
            })
            ->firstOrFail();

        $complementaryProducts = [];
        
        if ($invoice->order->cart_id) {
            $cartComplementaryProductIds = $this->cartComplementaryProductRepository
                ->where('cart_id', $invoice->order->cart_id)
                ->pluck('complementary_product_id');
            
            if ($cartComplementaryProductIds->isNotEmpty()) {
                $complementaryProducts = $this->complementaryProductRepository
                    ->whereIn('id', $cartComplementaryProductIds)
                    ->get();
            }

        }

        return $this->downloadPDF(
            view('shop::customers.account.orders.pdf', compact('invoice', 'complementaryProducts'))->render(),
            'invoice-'.$invoice->created_at->format('d-m-Y')
        );
    }

    /**
     * Cancel action for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel($locale, $id)
    {
        $customer = auth()->guard('customer')->user();

        /* find by order id in customer's order */
        $order = $customer->orders()->find($id);

        /* if order id not found then process should be aborted with 404 page */
        if (! $order) {
            abort(404);
        }

        $result = $this->orderRepository->cancel($order);

        if ($result) {
            session()->flash('success', trans('shop::app.customers.account.orders.view.cancel-success', ['name' => trans('admin::app.customers.account.orders.order')]));
        } else {
            session()->flash('error', trans('shop::app.customers.account.orders.view.cancel-error', ['name' => trans('admin::app.customers.account.orders.order')]));
        }

        return redirect()->back();
    }
}
