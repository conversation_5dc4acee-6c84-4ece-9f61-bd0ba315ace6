<?php

namespace Webkul\Shop\Http\Controllers\Customer\Account;

use Webkul\Checkout\Facades\Cart;
use Webkul\Core\Traits\PDFHandler;
use Webkul\Sales\Repositories\InvoiceRepository;
use Webkul\Sales\Repositories\OrderRepository;
use Webkul\Shop\DataGrids\OrderDataGrid;
use Webkul\Shop\Http\Controllers\Controller;
use Webkul\Checkout\Repositories\CartComplementaryProductRepository;
use Webkul\Product\Repositories\ComplementaryProductRepository;

class OrderController extends Controller
{
    use PDFHandler;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected OrderRepository $orderRepository,
        protected InvoiceRepository $invoiceRepository,
        protected CartComplementaryProductRepository $cartComplementaryProductRepository,
        protected ComplementaryProductRepository $complementaryProductRepository
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index($locale)
    {
        if (request()->ajax()) {
            return datagrid(OrderDataGrid::class)->process();
        }

        return view('shop::customers.account.orders.index');
    }

    /**
     * Show the view for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function view($locale, $id)
    {
        $order = $this->orderRepository->findOneWhere([
            'customer_id' => auth()->guard('customer')->id(),
            'id'          => $id,
        ]);

        abort_if(! $order, 404);

        $complementaryProducts = [];
        
        if ($order->cart_id) {
            $cartComplementaryProductIds = $this->cartComplementaryProductRepository
                ->where('cart_id', $order->cart_id)
                ->pluck('complementary_product_id');
            
            if ($cartComplementaryProductIds->isNotEmpty()) {
                $complementaryProducts = $this->complementaryProductRepository
                    ->whereIn('id', $cartComplementaryProductIds)
                    ->get();
            }
        }

        return view('shop::customers.account.orders.view', compact('order', 'complementaryProducts'));
    }

    /**
     * Reorder action for the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function reorder($locale, int $id)
    {
        $order = $this->orderRepository->findOrFail($id);

        foreach ($order->items as $item) {
            try {
                Cart::addProduct($item->product, $item->additional);
            } catch (\Exception $e) {
                // do nothing
            }
        }

        return redirect(route('shop.checkout.cart.index', app()->getLocale()));
    }

    /**
     * Print and download the for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function printInvoice($locale, $id)
    {
        $invoice = $this->invoiceRepository->where('id', $id)
            ->whereHas('order', function ($query) {
                $query->where('customer_id', auth()->guard('customer')->id());
            })
            ->firstOrFail();

        $complementaryProducts = [];

        if ($invoice->order->cart_id) {
            $cartComplementaryProductIds = $this->cartComplementaryProductRepository
                ->where('cart_id', $invoice->order->cart_id)
                ->pluck('complementary_product_id');

            if ($cartComplementaryProductIds->isNotEmpty()) {
                $complementaryProducts = $this->complementaryProductRepository
                    ->whereIn('id', $cartComplementaryProductIds)
                    ->get();
            }

            // Get all possible sizes
            $sizes = ['2 ml', '10 ml'];

            // Initialize empty array with default values for all sizes
            $defaultProducts = array_fill_keys($sizes, [
                'key' => '0pcs',
                'value' => ''
            ]);

            // Group products by size and build dynamic data
            $groupedProducts = $complementaryProducts->groupBy('size')->map(function ($products) {
                $count = $products->count();
                $size = trim($products->first()->size);

                return [
                    'key' => "{$count}pcs",
                    'value' => $products->pluck('name')->implode(', ')
                ];
            })->toArray();

            // Merge default and actual products, with size as part of value rather than key
            $complementaryProducts = array_map(function($size) use ($groupedProducts) {
                $sizeKey = trim($size);
                return array_merge([
                    'size' => $sizeKey,
                    'key' => isset($groupedProducts[$sizeKey]) ? $groupedProducts[$sizeKey]['key'] : '0pcs',
                    'value' => isset($groupedProducts[$sizeKey]) ? $groupedProducts[$sizeKey]['value'] : ''
                ]);
            }, $sizes);
        }

        return $this->downloadPDF(
            view('shop::customers.account.orders.pdf', compact('invoice', 'complementaryProducts'))->render(),
            'invoice-'.$invoice->created_at->format('d-m-Y')
        );
    }

    /**
     * Cancel action for the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel($locale, $id)
    {
        $customer = auth()->guard('customer')->user();

        /* find by order id in customer's order */
        $order = $customer->orders()->find($id);

        /* if order id not found then process should be aborted with 404 page */
        if (! $order) {
            abort(404);
        }

        $result = $this->orderRepository->cancel($order);

        if ($result) {
            session()->flash('success', trans('shop::app.customers.account.orders.view.cancel-success', ['name' => trans('admin::app.customers.account.orders.order')]));
        } else {
            session()->flash('error', trans('shop::app.customers.account.orders.view.cancel-error', ['name' => trans('admin::app.customers.account.orders.order')]));
        }

        return redirect()->back();
    }
}
