<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="@lang('shop::app.checkout.onepage.index.checkout')"/>

    <meta name="keywords" content="@lang('shop::app.checkout.onepage.index.checkout')"/>
@endPush

<x-shop::layouts
    :has-header="true"
    :has-feature="false"
    :has-footer="true"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.checkout.onepage.index.checkout')
    </x-slot>

    <v-checkout>
        <!-- Shimmer Effect -->
        <x-shop::shimmer.checkout.onepage />
    </v-checkout>

    @pushOnce('scripts')
        <script src="https://js.stripe.com/v3/"></script>

        <script type="text/x-template" id="v-checkout-template">
            <template v-if="!cart">
                <x-shop::shimmer.checkout.onepage />
            </template>

            <template v-else>
                <div class="px-9">
                    <section class="pt-16 xl:pt-28 relative max-w-[1640px] mx-auto mb-[30px] px-5 2xl:px-0">
                        <h1 class="font-playfair pb-4 xl:pb-8 border-light-gray text-2xl xl:text-[32px] text-secondary">
                            @lang('shop::app.checkout.onepage.index.order-preview')
                        </h1>

                        <div class="w-full pt-8 border-y border-light-gray">
                            <div v-for="item in cart.items" class="flex max-lg:flex-col items-center justify-between pb-8 max-lg:gap-5">
                                <div class="cursor-pointer flex sm:items-center flex-1  gap-5 self-stretch">
                                    <div class="img-box h-full">
                                        {!! view_render_event('bagisto.shop.checkout.onepage.summary.item_image.before') !!}
                                        <img class="max-w-[120px] sm:max-w-[213px] object-cover h-full" :src="item.base_image.original_image_url" :alt="item.name"/>
                                        {!! view_render_event('bagisto.shop.checkout.onepage.summary.item_image.after') !!}
                                    </div>

                                    <div class="flex flex-col justify-between flex-1 h-full">
                                        <div class="flex flex-col gap-2.5 text-sm text-start lg:pt-5">
                                            {!! view_render_event('bagisto.shop.checkout.onepage.summary.item_name.before') !!}

                                            <h4 class="uppercase font-bold font-playfair text-base  sm:text-lg leading-8 text-secondary">
                                                @{{ item.categories && item.categories.length > 0 ? item.categories[0].name : '' }}/@{{ item.name }}
                                            </h4>

                                            {!! view_render_event('bagisto.shop.checkout.onepage.summary.item_name.after') !!}

                                            <template v-if="displayTax.prices == 'including_tax'">
                                                <p class="text-dark-gray-2 text-sm: sm:text-lg">@{{ item.formatted_price_incl_tax }}</p>
                                            </template>

                                            <template v-else-if="displayTax.prices == 'both'">
                                                <p class="text-dark-gray-2 text-sm: sm:text-lg">@{{ item.formatted_price_incl_tax }}</p>

                                                <span class="text-xs font-normal">
                                                    @lang('shop::app.checkout.onepage.summary.excl-tax')
                                                    <p class="text-dark-gray-2 text-sm: sm:text-lg">@{{ item.formatted_total }}</p>
                                                </span>
                                            </template>

                                            <template v-else>
                                                <p class="text-dark-gray-2 text-sm: sm:text-lg">@{{ item.formatted_price }}</p>
                                            </template>

                                            <p class="text-dark-gray-2 text-sm: sm:text-lg">@lang('shop::app.checkout.onepage.summary.qty'): @{{ item.quantity }}</p>
                                        </div>

                                        <p class="text-black text-base">
                                            @lang('shop::app.checkout.onepage.summary.availability'):
                                            <span v-if="item?.stock_qty >= item.quantity">@lang('shop::app.checkout.onepage.summary.in-stock')</span>
                                            <span v-else>@lang('shop::app.checkout.onepage.summary.out-of-stock')</span>
                                        </p>
                                    </div>
                                </div>

                                <div class="w-full flex items-center flex-1">
                                    <div class="flex-1">
                                        <div class="text-dark-gray flex items-start justify-start border border-[#CCC] w-fit">
                                            {!! view_render_event('bagisto.shop.checkout.cart.quantity_changer.before') !!}

                                            <x-shop::quantity-changer
                                                class="text-dark-gray flex justify-start"
                                                name="quantity"
                                                ::max="item?.stock_qty"
                                                ::value="item?.quantity"
                                                @change="update($event, item)"
                                            />

                                            {!! view_render_event('bagisto.shop.checkout.cart.quantity_changer.after') !!}
                                        </div>
                                    </div>

                                    {!! view_render_event('bagisto.shop.checkout.cart.total.before') !!}

                                    <template v-if="displayTax.prices == 'including_tax'">
                                        <h6 class="flex-1 text-right text-[22px] leading-7 text-dark-gray">@{{ item.formatted_total_incl_tax }}</h6>
                                    </template>

                                    <template v-else-if="displayTax.prices == 'both'">
                                        <h6 class="flex flex-col text-nowrap text-right text-[22px] leading-7 text-dark-gray">
                                            @{{ item.formatted_total_incl_tax }}
                                            <span class="text-xs font-normal">
                                                @lang('shop::app.checkout.cart.index.excl-tax')
                                                <span class="font-medium">@{{ item.formatted_total }}</span>
                                            </span>
                                        </h6>
                                    </template>

                                    <template v-else>
                                        <h6 class="flex-1 text-right text-[22px] leading-7 text-dark-gray">@{{ item.formatted_total }}</h6>
                                    </template>

                                    {!! view_render_event('bagisto.shop.checkout.cart.total.after') !!}
                                </div>
                            </div>
                        </div>
                    </section>

                    <section v-if="complementaryProducts.length > 0" class="max-w-[1640px] mx-auto max-2xl:px-5 mb-5">
                        <h4 class="text-dark-gray font-bold text-base leadinding-[22px] mb-[30px]">
                            @lang('shop::app.checkout.onepage.index.selected-samples')
                        </h4>

                        <div class="border-b border-light-gray grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7 2xl:grid-cols-8 gap-2.5 pb-[30px]">
                            <div v-for="product in complementaryProducts" class="bg-primary flex flex-col items-center py-4">
                                <div class="max-w-[132px] max-h-[132px] mb-2">
                                    <img :src="product.logo_url" class="w-full h-full object-contain" :alt="product.name">
                                </div>

                                <h4 class="font-playfair text-base leading-[22px] text-dark-gray mb-[6px] tracking-[0.8px]">
                                    @{{ product.name }}
                                </h4>
                            </div>
                        </div>
                    </section>

                    <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-20 2xl:gap-[140px] max-w-[1640px] mx-auto py-[50px] px-5 2xl:px-0 mb-5">
                        <div>
                            <div class="border border-light-gray mb-8">
                                <div class="h-14 flex items-center justify-between border-b border-light-gray px-5">
                                    <h4 class="font-bold  text-dark-gray capitalize leading-snug">@lang('shop::app.checkout.onepage.address.title')</h4>
                                    <img src="{{ asset('widian-assets/images/address.svg') }}" alt="">
                                </div>

                                <div :class="selectedAddress ? 'p-5' : 'p-0'">
                                    <div v-if="selectedAddress" class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-bold  text-dark-gray capitalize leading-snug mb-[10px]">@{{ selectedAddress.first_name }} @{{ selectedAddress.last_name }}</h4>
                                            <p class="text-sm text-dark-gray-2 leading-tight line-clamp-1">
                                                @{{ selectedAddress.address[0] }}, @{{ selectedAddress ? selectedAddress.city : '' }}, @{{ selectedAddress ? selectedAddress.state : '' }}, @{{ selectedAddress ? selectedAddress.country : '' }}, @{{ selectedAddress ? selectedAddress.postcode : '' }}
                                            </p>
                                        </div>
                                        @if(!auth()->guard('customer')->check())
                                            <button @click="$refs.createAddressDrawer.open()" type="button" class="uppercase text-xs leading-none text-white bg-secondary hover:bg-black duration-300 rounded-[5px] py-2.5 px-3">
                                                @lang('shop::app.checkout.onepage.address.change')
                                            </button>
                                        @else
                                            <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
                                                <form @submit.prevent>
                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.before') !!}

                                                    <x-shop::drawer ref="addressDrawer">
                                                        <x-slot:toggle>
                                                            <div v-if="!selectedAddress"></div>
                                                            <div v-else class="flex gap-3">
                                                                <button type="button" class="uppercase text-xs leading-none text-white bg-secondary hover:bg-black duration-300 rounded-[5px] py-2.5 px-3">
                                                                    @lang('shop::app.checkout.onepage.address.change')
                                                                </button>

                                                                <x-shop::button
                                                                    v-if="cart.shipping_address == null"
                                                                    type="button"
                                                                    class="uppercase text-xs leading-none text-white bg-secondary hover:bg-black duration-300 rounded-[5px] py-2.5 px-3"
                                                                    :title="trans('shop::app.checkout.onepage.address.confirm')"
                                                                    @click="selectAddress(customerSavedAddresses[activeAddressForm].find(a => a.id === selectedAddresses[activeAddressForm + '_address_id']), activeAddressForm)"
                                                                    ::loading="isStoring"
                                                                    ::disabled="isStoring"
                                                                />
                                                            </div>
                                                        </x-slot>

                                                        <x-slot:header>
                                                            {!! view_render_event('bagisto.shop.checkout.add.address.header.before') !!}

                                                            <h2 class="text-2xl font-medium max-md:text-base">
                                                                <span>@lang('shop::app.checkout.onepage.address.addresses')</span>
                                                            </h2>

                                                            {!! view_render_event('bagisto.shop.checkout.add.address.header.after') !!}
                                                        </x-slot>

                                                        <x-slot:content class="py-5">
                                                            <div class="mb-4 flex items-center justify-between max-md:mb-2">
                                                                <h2 class="text-xl font-medium max-sm:text-base max-sm:font-normal">
                                                                    @lang('shop::app.checkout.onepage.address.billing-address')
                                                                </h2>
                                                            </div>

                                                            <div class="mb-2 grid grid-cols-2 gap-5 max-1060:grid-cols-[1fr] max-lg:grid-cols-2 max-md:mt-2 max-md:grid-cols-1">
                                                                <div v-for="address in customerSavedAddresses.billing" class="relative max-w-[414px] cursor-pointer select-none rounded-xl border border-zinc-200 p-0 max-md:flex-wrap max-md:rounded-lg">
                                                                    <div class="absolute top-5 flex gap-2 ltr:right-5 rtl:left-5">
                                                                        <x-shop::form.control-group class="!mb-0 flex items-center gap-2.5">
                                                                            <x-shop::form.control-group.control
                                                                                type="radio"
                                                                                name="billing.id"
                                                                                ::id="`onepage_billing_address_id_${address.id}`"
                                                                                ::for="`onepage_billing_address_id_${address.id}`"
                                                                                ::value="address.id"
                                                                                v-model="selectedAddresses.billing_address_id"
                                                                                rules="required"
                                                                                label="{{ trans('shop::app.checkout.onepage.address.billing-address') }}"
                                                                            />
                                                                        </x-shop::form.control-group>
                                                                    </div>

                                                                    <label :for="`onepage_billing_address_id_${address.id}`" class="block cursor-pointer rounded-xl p-5 max-sm:rounded-lg">
                                                                        <span class="icon-checkout-address text-6xl text-navyBlue max-sm:text-5xl"></span>

                                                                        <div class="flex items-center justify-between">
                                                                            <p class="text-base font-medium">
                                                                                @{{ address.first_name + ' ' + address.last_name }}

                                                                                <template v-if="address.company_name">
                                                                                    (@{{ address.company_name }})
                                                                                </template>
                                                                            </p>
                                                                        </div>

                                                                        <p class="mt-6 text-sm text-zinc-500 max-md:mt-2 max-sm:mt-0">
                                                                            <template v-if="address.address">
                                                                                @{{ address.address.join(', ') }},
                                                                            </template>

                                                                            @{{ address.city }},
                                                                            @{{ address.state }}, @{{ address.country }},
                                                                            @{{ address.postcode }}
                                                                        </p>
                                                                    </label>
                                                                </div>

                                                                <div @click="openAddressForm('billing')" class="flex max-w-[414px] cursor-pointer items-center justify-center rounded-xl border border-zinc-200 p-5 max-md:flex-wrap max-md:rounded-lg">
                                                                    <div class="flex items-center gap-x-2.5" role="button" tabindex="0">
                                                                        <span class="icon-plus rounded-full border border-black p-2.5 text-3xl max-sm:p-2" role="presentation"></span>
                                                                        <p class="text-base">@lang('shop::app.checkout.onepage.address.add-new')</p>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Error Message Block -->
                                                            <x-shop::form.control-group.error name="billing.id" />

                                                            <!-- Shipping Address Block if have stockable items -->
                                                            <template v-if="cart.have_stockable_items">
                                                                <!-- Use for Shipping Checkbox -->
                                                                <x-shop::form.control-group class="!mb-0 mt-5 flex items-center gap-2.5">
                                                                    <x-shop::form.control-group.control
                                                                        type="checkbox"
                                                                        name="billing.use_for_shipping"
                                                                        id="onepage_use_for_shipping"
                                                                        for="onepage_use_for_shipping"
                                                                        value="1"
                                                                        @change="useBillingAddressForShipping = ! useBillingAddressForShipping"
                                                                        ::checked="!! useBillingAddressForShipping"
                                                                    />

                                                                    <label
                                                                        class="cursor-pointer select-none text-base text-zinc-500 max-md:text-sm max-sm:text-xs ltr:pl-0 rtl:pr-0"
                                                                        for="onepage_use_for_shipping"
                                                                    >
                                                                        @lang('shop::app.checkout.onepage.address.same-as-billing')
                                                                    </label>
                                                                </x-shop::form.control-group>

                                                                <!-- Customer Shipping Address -->
                                                                <div
                                                                    class="mt-8"
                                                                    v-if="! useBillingAddressForShipping"
                                                                >
                                                                    <!-- Shipping Address Header -->
                                                                    <div class="mb-4 flex items-center justify-between">
                                                                        <h2 class="text-xl font-medium max-md:text-lg max-sm:text-base">
                                                                            @lang('shop::app.checkout.onepage.address.shipping-address')
                                                                        </h2>
                                                                    </div>

                                                                    <!-- Saved Customer Addresses Cards -->
                                                                    <div class="mb-2 grid grid-cols-2 gap-5 max-1060:grid-cols-[1fr] max-lg:grid-cols-2 max-md:mt-4 max-md:grid-cols-1">
                                                                        <div
                                                                            class="relative max-w-[414px] cursor-pointer select-none rounded-xl border border-zinc-200 p-0 max-md:flex-wrap max-md:rounded-lg"
                                                                            v-for="address in customerSavedAddresses.shipping"
                                                                        >
                                                                            <!-- Actions -->
                                                                            <div class="absolute top-5 flex gap-5 ltr:right-5 rtl:left-5">
                                                                                <x-shop::form.control-group class="!mb-0 flex items-center gap-2.5">
                                                                                    <x-shop::form.control-group.control
                                                                                        type="radio"
                                                                                        name="shipping.id"
                                                                                        ::id="`onepage_shipping_address_id_${address.id}`"
                                                                                        ::for="`onepage_shipping_address_id_${address.id}`"
                                                                                        ::value="address.id"
                                                                                        v-model="selectedAddresses.shipping_address_id"
                                                                                        rules="required"
                                                                                        label="{{ trans('shop::app.checkout.onepage.address.shipping-address') }}"
                                                                                    />
                                                                                </x-shop::form.control-group>
                                                                            </div>

                                                                            <!-- Details -->
                                                                            <label
                                                                                class="block cursor-pointer rounded-xl p-5 max-md:rounded-lg"
                                                                                :for="`onepage_shipping_address_id_${address.id}`"
                                                                            >
                                                                                <span class="icon-checkout-address text-6xl text-navyBlue max-sm:text-5xl"></span>

                                                                                <div class="flex items-center justify-between">
                                                                                    <p class="text-base font-medium">
                                                                                        @{{ address.first_name + ' ' + address.last_name }}

                                                                                        <template v-if="address.company_name">
                                                                                            (@{{ address.company_name }})
                                                                                        </template>
                                                                                    </p>
                                                                                </div>

                                                                                <p class="mt-6 text-sm text-zinc-500 max-md:mt-2 max-sm:mt-0">
                                                                                    <template v-if="address.address">
                                                                                        @{{ address.address.join(', ') }},
                                                                                    </template>

                                                                                    @{{ address.city }},
                                                                                    @{{ address.state }}, @{{ address.country }},
                                                                                    @{{ address.postcode }}
                                                                                </p>
                                                                            </label>
                                                                        </div>

                                                                        <!-- New Address Card -->
                                                                        <div @click="openAddressForm('shipping')" class="flex max-w-[414px] cursor-pointer items-center justify-center rounded-xl border border-zinc-200 p-5 max-md:flex-wrap max-md:rounded-lg">
                                                                            <div class="flex items-center gap-x-2.5" role="button" tabindex="0">
                                                                                <span class="icon-plus rounded-full border border-black p-2.5 text-3xl max-sm:p-2" role="presentation"></span>
                                                                                <p class="text-base">@lang('shop::app.checkout.onepage.address.add-new')</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <x-shop::form.control-group.error name="shipping.id" />
                                                                </div>
                                                            </template>
                                                        </x-slot>

                                                        <x-slot:footer>
                                                            <div class="flex flex-wrap items-center gap-4 justify-end">
                                                                <x-shop::button
                                                                    type="button"
                                                                    class="bg-secondary text-white m-0 block rounded-lg px-10 py-2.5 text-center max-md:w-full max-md:max-w-full"
                                                                    :title="trans('shop::app.checkout.onepage.address.save')"
                                                                    @click="selectAddress(customerSavedAddresses[activeAddressForm].find(a => a.id === selectedAddresses[activeAddressForm + '_address_id']), activeAddressForm)"
                                                                    ::loading="isStoring"
                                                                    ::disabled="isStoring"
                                                                />
                                                            </div>
                                                        </x-slot>
                                                    </x-shop::drawer>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.after') !!}
                                                </form>
                                            </x-shop::form>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex items-center justify-between" :class="selectedAddress ? 'p-0' : 'p-5'">
                                    <div v-if="!selectedAddress">
                                        <h4 class="font-bold  text-dark-gray capitalize leading-snug mb-[10px]">@lang('shop::app.checkout.onepage.address.no-address')</h4>
                                        <p class="text-sm text-dark-gray-2 leading-tight line-clamp-1">@lang('shop::app.checkout.onepage.address.add-new-address')</p>
                                    </div>
                                    <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
                                        <form @submit.prevent>
                                            {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.before') !!}

                                            <x-shop::drawer ref="createAddressDrawer">
                                                <x-slot:toggle>
                                                    <div v-if="selectedAddress"></div>
                                                    <button v-else type="button" class="uppercase text-xs leading-none text-white bg-secondary hover:bg-black duration-300 rounded-[5px] py-2.5 px-3">
                                                        @lang('shop::app.checkout.onepage.address.add-new')
                                                    </button>
                                                </x-slot>

                                                <x-slot:header>
                                                    {!! view_render_event('bagisto.shop.checkout.add.address.header.before') !!}

                                                    <h2 class="text-2xl font-medium max-md:text-base">
                                                        <span v-if="activeAddressForm === 'billing'">
                                                            @lang('shop::app.checkout.onepage.address.add-billing-address')
                                                        </span>
                                                        <span v-else-if="activeAddressForm === 'shipping'">
                                                            @lang('shop::app.checkout.onepage.address.add-shipping-address')
                                                        </span>
                                                    </h2>

                                                    {!! view_render_event('bagisto.shop.checkout.add.address.header.after') !!}
                                                </x-slot>

                                                <!-- Billing Address Form -->
                                                <x-slot:content class="py-5">
                                                    {!! view_render_event('bagisto.shop.checkout.add.address.content.before') !!}

                                                    <x-shop::form.control-group class="hidden">
                                                        <x-shop::form.control-group.control
                                                            type="text"
                                                            ::name="activeAddressForm + '.id'"
                                                            ::value="addressForm.id"
                                                        />
                                                    </x-shop::form.control-group>

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm required">
                                                            @lang('shop::app.customers.account.addresses.create.first-name')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            type="text"
                                                            ::name="activeAddressForm + '.first_name'"
                                                            rules="required"
                                                            v-model="addressForm.first_name"
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            :label="trans('shop::app.customers.account.addresses.create.first-name')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.first-name')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.first_name'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.first_name.after') !!}

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm required">
                                                            @lang('shop::app.customers.account.addresses.create.last-name')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="text"
                                                            ::name="activeAddressForm + '.last_name'"
                                                            rules="required"
                                                            v-model="addressForm.last_name"
                                                            :label="trans('shop::app.customers.account.addresses.create.last-name')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.last-name')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.last_name'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.last_name.after') !!}

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm required">
                                                            @lang('shop::app.customers.account.addresses.create.email')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="text"
                                                            ::name="activeAddressForm + '.email'"
                                                            rules="required|email"
                                                            v-model="addressForm.email"
                                                            :label="trans('shop::app.customers.account.addresses.create.email')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.email')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.email'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.email.after') !!}

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm required">
                                                            @lang('shop::app.customers.account.addresses.create.phone')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="text"
                                                            ::name="activeAddressForm + '.phone'"
                                                            rules="required|phone"
                                                            v-model="addressForm.phone"
                                                            :label="trans('shop::app.customers.account.addresses.create.phone')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.phone')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.phone'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.phone.after') !!}

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm required">
                                                            @lang('shop::app.customers.account.addresses.create.street-address')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="text"
                                                            ::name="activeAddressForm + '.address.[0]'"
                                                            rules="required|address"
                                                            v-model="addressForm.address[0]"
                                                            :label="trans('shop::app.customers.account.addresses.create.street-address')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.street-address')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.address.[0]'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.street_address.after') !!}

                                                    @if (core()->getConfigData('customer.address.information.street_lines') && core()->getConfigData('customer.address.information.street_lines') > 1)
                                                        @for ($i = 1; $i < core()->getConfigData('customer.address.information.street_lines'); $i++)
                                                            <x-shop::form.control-group.control
                                                                class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                                type="text"
                                                                ::name="activeAddressForm + '.address.[{{ $i }}]'"
                                                                :value="old('address.[{{ $i }}]')"
                                                                rules="address"
                                                                :label="trans('shop::app.customers.account.addresses.create.street-address')"
                                                                :placeholder="trans('shop::app.customers.account.addresses.create.street-address')"
                                                            />

                                                            <x-shop::form.control-group.error
                                                                class="mb-2"
                                                                ::name="activeAddressForm + '.address.[{{ $i }}]'"
                                                            />
                                                        @endfor
                                                    @endif

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.street_address.after') !!}

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm {{ core()->isCountryRequired() ? 'required' : '' }}">
                                                            @lang('shop::app.customers.account.addresses.create.country')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="select"
                                                            ::name="activeAddressForm + '.country'"
                                                            v-model="selectedCountry"
                                                            rules="{{ core()->isCountryRequired() ? 'required' : '' }}"
                                                            :aria-label="trans('shop::app.customers.account.addresses.create.country')"
                                                            :label="trans('shop::app.customers.account.addresses.create.country')"
                                                        >
                                                            <option value="">
                                                                @lang('shop::app.customers.account.addresses.create.select-country')
                                                            </option>

                                                            <option
                                                                v-for="country in countries"
                                                                :value="country.code"
                                                            >
                                                                @{{ country.name }}
                                                            </option>
                                                        </x-shop::form.control-group.control>

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.country'" />
                                                    </x-shop::form.control-group>

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm {{ core()->isStateRequired() ? 'required' : '' }}">
                                                            @lang('shop::app.customers.account.addresses.create.state')
                                                        </x-shop::form.control-group.label>

                                                        <template v-if="haveStates">
                                                            <x-shop::form.control-group.control
                                                                class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                                type="select"
                                                                id="state"
                                                                ::name="activeAddressForm + '.state'"
                                                                rules="{{ core()->isStateRequired() ? 'required' : '' }}"
                                                                v-model="addressForm.state"
                                                                :label="trans('shop::app.customers.account.addresses.create.state')"
                                                                :placeholder="trans('shop::app.customers.account.addresses.create.state')"
                                                            >
                                                                <option
                                                                    v-for='(state, index) in states[selectedCountry]'
                                                                    :value="state.code"
                                                                >
                                                                    @{{ state.default_name }}
                                                                </option>
                                                            </x-shop::form.control-group.control>
                                                        </template>

                                                        <template v-else>
                                                            <x-shop::form.control-group.control
                                                                class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                                type="text"
                                                                ::name="activeAddressForm + '.state'"
                                                                v-model="addressForm.state"
                                                                rules="{{ core()->isStateRequired() ? 'required' : '' }}"
                                                                :label="trans('shop::app.customers.account.addresses.create.state')"
                                                                :placeholder="trans('shop::app.customers.account.addresses.create.state')"
                                                            />
                                                        </template>

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.state'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.state.after') !!}

                                                    <x-shop::form.control-group class="mb-3">
                                                        <x-shop::form.control-group.label class="text-sm required">
                                                            @lang('shop::app.customers.account.addresses.create.city')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="text"
                                                            ::name="activeAddressForm + '.city'"
                                                            rules="required"
                                                            v-model="addressForm.city"
                                                            :label="trans('shop::app.customers.account.addresses.create.city')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.city')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.city'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.city.after') !!}

                                                    <x-shop::form.control-group v-if="!isPostcodeHidden" class="mb-0">
                                                        <x-shop::form.control-group.label class="text-sm {{ core()->isPostCodeRequired() ? 'required' : '' }}">
                                                            @lang('shop::app.customers.account.addresses.create.post-code')
                                                        </x-shop::form.control-group.label>

                                                        <x-shop::form.control-group.control
                                                            class="my-1.5 w-full rounded-lg border px-3 py-2 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 max-sm:px-4 max-md:py-2 max-sm:text-sm"
                                                            type="text"
                                                            ::name="activeAddressForm + '.postcode'"
                                                            rules="{{ core()->isPostCodeRequired() ? 'required' : '' }}|postcode"
                                                            v-model="addressForm.postcode"
                                                            :label="trans('shop::app.customers.account.addresses.create.post-code')"
                                                            :placeholder="trans('shop::app.customers.account.addresses.create.post-code')"
                                                        />

                                                        <x-shop::form.control-group.error ::name="activeAddressForm + '.postcode'" />
                                                    </x-shop::form.control-group>

                                                    {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.postcode.after') !!}

                                                    <div class="text-md flex select-none items-center gap-x-1.5 text-zinc-500">
                                                        <input
                                                            type="hidden"
                                                            name="default_address"
                                                            v-model="addressForm.default_address"
                                                            id="default_address"
                                                            class="peer hidden cursor-pointer"
                                                        >
                                                    </div>
                                                </x-slot>

                                                <x-slot:footer>
                                                    <div class="flex flex-wrap items-center gap-4 justify-end">
                                                        @if(auth()->guard('customer')->check())
                                                            <x-shop::button
                                                                class="bg-secondary text-white m-0 block rounded-lg px-10 py-2.5 text-center max-md:w-full max-md:max-w-full"
                                                                :title="trans('shop::app.customers.account.addresses.create.save')"
                                                                @click="saveAddress"
                                                                ::loading="isStoring"
                                                                ::disabled="isStoring"
                                                            />
                                                        @else
                                                            <x-shop::button
                                                                class="bg-secondary text-white m-0 block rounded-lg px-10 py-2.5 text-center max-md:w-full max-md:max-w-full"
                                                                :title="trans('shop::app.customers.account.addresses.create.save')"
                                                                @click="saveGuestAddress"
                                                                ::loading="isStoring"
                                                                ::disabled="isStoring"
                                                            />
                                                        @endif
                                                    </div>
                                                </x-slot>
                                            </x-shop::drawer>

                                            {!! view_render_event('bagisto.shop.customers.account.addresses.create_form_controls.after') !!}
                                        </form>
                                    </x-shop::form>
                                </div>
                            </div>

                            <div v-if="selectedAddress && shippingMethods.length" class="border border-light-gray mb-8">
                                <div class="h-14 flex items-center justify-between border-b border-light-gray px-5">
                                    <h4 class="font-bold  text-dark-gray capitalize leading-snug">
                                        @lang('shop::app.checkout.onepage.shipping.shipping-method')
                                    </h4>

                                    <img src="{{ asset('widian-assets/images/truck.svg') }}" alt="">
                                </div>

                                <template v-for="(method, key) in shippingMethods" :key="key">
                                    {!! view_render_event('bagisto.shop.checkout.onepage.shipping.before') !!}

                                    <div v-for="rate in method.rates" :key="rate.method" class="flex items-center justify-between p-5 border-b border-light-gray">
                                        <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer">
                                            <div class="relative w-5 h-5">
                                                <input
                                                    type="radio"
                                                    name="shipping_method"
                                                    :id="rate.method"
                                                    :value="rate.method"
                                                    v-model="selectedShippingMethod"
                                                    @change="saveShippingMethod(rate.method)"
                                                    :checked="rate.method == cart.shipping_method"
                                                    class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                                />
                                                <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                            </div>

                                            <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                                <p class="text-base leading-snug text-dark-gray font-bold mb-1">
                                                    @{{ rate.method_title }}
                                                </p>

                                                <p class="text-sm text-dark-gray-2 leading-tight">@{{ rate.method_description }}</p>
                                            </div>
                                        </label>

                                        <p class="text-base leading-snug text-dark-gray font-bold">@{{ rate.base_formatted_price }}</p>
                                    </div>

                                    {!! view_render_event('bagisto.shop.checkout.onepage.shipping.after') !!}
                                </template>
                            </div>

                            <textarea
                                name="order_comments"
                                v-model="orderComments"
                                placeholder="@lang('shop::app.checkout.onepage.summary.add-comments-for-order')"
                                class="text-base text-dark-gray-3 resize-none h-[114px] p-5 border border-light-gray text-dark-gray-3 outline-none w-full mb-[30px]"
                            ></textarea>

                            <ul class="flex flex-col gap-4 mb-[30px]">
                                <li class="flex gap-2 md:gap-4 items-center">
                                    <label class="flex items-center cursor-pointer relative">
                                        <input
                                            id="gift-wrap"
                                            type="checkbox"
                                            class="peer h-5 w-5 rounded-[2px] cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent"
                                            name="is_gift"
                                            v-model="isGift"
                                        />
                                        <span class="absolute h-3 w-3 block text-white opacity-0 bg-secondary peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </label>

                                    <label for="gift-wrap" class="text-xs md:text-sm lg:text-base text-dark-gray cursor-pointer">
                                        @lang('shop::app.checkout.onepage.summary.wrap-order')
                                    </label>
                                </li>

                                <li class="flex gap-2 md:gap-4 items-center">
                                    <label class="flex items-center cursor-pointer relative">
                                        <input
                                            id="terms-conditions"
                                            type="checkbox"
                                            class="peer h-5 w-5 rounded-[2px] cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent"
                                            name="terms_of_service"
                                            v-model="termsOfService"
                                            {{ (Auth::check() && Auth::user()->terms_conditions) ? 'disabled' : '' }}
                                        />
                                        <span class="absolute h-3 w-3 block text-white opacity-0 bg-secondary peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </label>

                                    <label for="terms-conditions" class="text-xs md:text-sm lg:text-base text-dark-gray cursor-pointer">
                                        @lang('shop::app.checkout.onepage.summary.terms-service')*
                                    </label>
                                </li>

                                <li class="flex gap-2 md:gap-4 items-center">
                                    <label class="flex items-center cursor-pointer relative">
                                        <input
                                            id="promotion-emails"
                                            type="checkbox"
                                            class="peer h-5 w-5 rounded-[2px] cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent"
                                            name="promotion_emails"
                                            v-model="promotionEmails"
                                            {{ (Auth::check() && Auth::user()->subscribed_to_news_letter == 1) ? 'disabled' : '' }}
                                        />
                                        <span class="absolute h-3 w-3 block text-white opacity-0 bg-secondary peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </label>

                                    <label for="promotion-emails" class="text-xs md:text-sm lg:text-base text-dark-gray cursor-pointer">
                                        @lang('shop::app.checkout.onepage.summary.promotion-email')
                                    </label>
                                </li>
                            </ul>
                        </div>

                        <!--Order Summary-->
                        <div>
                            <div class="border border-light-gray mb-[30px]">
                                <h4 class="flex items-center font-bold border-b border-light-gray text-dark-gray h-14 px-5 capitalize leading-snug">
                                    @lang('shop::app.checkout.coupon.title')
                                </h4>

                                <!-- Apply Coupon Form -->
                                <x-shop::form
                                    v-slot="{ meta, errors, handleSubmit }"
                                    as="div"
                                    >
                                    <!-- Apply coupon form -->
                                    <form @submit="handleSubmit($event, applyCoupon)">
                                        {!! view_render_event('bagisto.shop.checkout.cart.coupon.coupon_form_controls.before') !!}

                                            <x-shop::form.control-group class="p-5 flex max-sm:flex-col items-center gap-5">
                                                <x-shop::form.control-group.control
                                                    type="text"
                                                    class="max-sm:w-full grow border border-light-gray rounded-[5px] h-11 px-[15px] text-dark-gray-2 text-sm leading-none outline-none"
                                                    name="code"
                                                    rules="required"
                                                    :placeholder="trans('shop::app.checkout.coupon.enter-promo-code')"
                                                />

                                                <x-shop::button
                                                    class="max-sm:w-full min-w-40 rounded-[5px] h-11 flex items-center justify-center bg-secondary hover:bg-black duration-300 text-base leading-tight text-white"
                                                    :title="trans('shop::app.checkout.coupon.submit')"
                                                    ::loading="isStoring"
                                                    ::disabled="isStoring"
                                                />
                                            </x-shop::form.control-group>

                                            <x-shop::form.control-group.error
                                                v-if="!cart.coupon_code"
                                                class="flex px-5"
                                                control-name="code"
                                            />

                                        {!! view_render_event('bagisto.shop.checkout.cart.coupon.coupon_form_controls.after') !!}
                                    </form>
                                </x-shop::form>
                            </div>

                            <div class="mb-[30px]">
                                <p class="text-md md:text-[22px] leading-7 pb-[16px] text-dark-gray flex items-center justify-between">
                                    <span>@lang('shop::app.checkout.onepage.summary.sub-total') :</span>

                                    <template v-if="displayTax.subtotal == 'including_tax'">
                                        <span>@{{ cart.formatted_sub_total_incl_tax }}</span>
                                    </template>

                                    <template v-else-if="displayTax.subtotal == 'both'">
                                        <p class="flex flex-col text-3xl font-semibold max-md:text-sm max-sm:text-right">
                                            @{{ cart.formatted_sub_total_incl_tax }}

                                            <span class="text-sm font-normal text-zinc-500 max-sm:text-xs">
                                                @lang('shop::app.checkout.onepage.summary.excl-tax')

                                                <span>@{{ cart.formatted_sub_total }}</span>
                                            </span>
                                        </p>
                                    </template>

                                    <template v-else>
                                        <span>@{{ cart.formatted_sub_total }}</span>
                                    </template>
                                </p>

                                <p class="text-md md:text-[22px] leading-7 pb-[16px] text-dark-gray flex items-center justify-between">
                                    <span>@lang('shop::app.checkout.onepage.summary.tax') (VAT 5%) :</span>
                                    <span>@{{ cart.formatted_tax_total }}</span>
                                </p>

                                <p class="text-md md:text-[22px] leading-7 pb-[16px] text-dark-gray flex items-center justify-between">
                                    <template v-if="displayTax.shipping == 'including_tax'">
                                        <span>@lang('shop::app.checkout.onepage.summary.shipping') :</span>

                                        <span>@{{ cart.formatted_shipping_amount_incl_tax }}</span>
                                    </template>

                                    <template v-else-if="displayTax.shipping == 'both'">
                                        <div class="flex justify-between text-right">
                                            <p class="text-base max-sm:text-sm">@lang('shop::app.checkout.onepage.summary.shipping-charges-excl-tax')</p>

                                            <p class="text-base font-medium max-sm:text-sm">@{{ cart.formatted_shipping_amount }}</p>
                                        </div>

                                        <div class="flex justify-between text-right">
                                            <p class="text-base max-sm:text-sm">@lang('shop::app.checkout.onepage.summary.shipping-charges-incl-tax')</p>

                                            <p class="text-base font-medium max-sm:text-sm">@{{ cart.formatted_shipping_amount_incl_tax }}</p>
                                        </div>
                                    </template>

                                    <template v-else>
                                        <span>
                                            @lang('shop::app.checkout.onepage.summary.shipping')
                                            <span v-if="cart.selected_shipping_method">(@{{ cart.selected_shipping_method }})</span> :
                                        </span>

                                        <span>@{{ cart.formatted_shipping_amount }}</span>
                                    </template>
                                </p>

                                <!-- Applied Coupon Information Container -->
                                <p v-if="cart.coupon_code" class="text-md md:text-[22px] leading-7 pb-[16px] text-dark-gray flex items-center justify-between">
                                    <span>@lang('shop::app.checkout.onepage.summary.discount') (@lang('shop::app.checkout.coupon.promo-code') @{{ cart.coupon_code }}) :
                                        <span
                                            class="icon-cancel cursor-pointer text-2xl max-sm:text-base"
                                            title="@lang('shop::app.checkout.coupon.remove')"
                                            @click="destroyCoupon"
                                        >
                                        </span>
                                    </span>
                                    <span>@{{ cart.formatted_discount_amount }}</span>
                                </p>
                            </div>

                            <div class="border-t border-light-gray text-dark-gray flex justify-between items-center py-[30px] font-bold text-xl leading-[28px]">
                                <span>@lang('shop::app.checkout.onepage.summary.bag-subtotal'):</span>
                                <span>@{{ cart.formatted_grand_total }}</span>
                            </div>


                            <div v-if="paymentMethods.length" class="border border-light-gray text-dark-gray font-bold text-xl leading-[28px] mb-[30px]">
                                <div class="h-14 flex items-center justify-between border-b border-light-gray px-5">
                                    <h4 class="font-bold text-dark-gray capitalize leading-snug">@lang('shop::app.checkout.onepage.payment.payment-method')</h4>
                                    <img src="{{ asset('widian-assets/images/card.svg') }}" alt="">
                                </div>

                                <div v-for="(payment, index) in paymentMethods" class="border-b border-light-gray" :class="{'border-b-0': index == (paymentMethods.length - 1)}">
                                    <div class="flex items-center justify-between p-5">
                                        <label class="flex gap-2 md:gap-[15px] items-center cursor-pointer" :for="payment.method">
                                            <div class="relative w-5 h-5">
                                                <input
                                                    type="radio"
                                                    name="payment[method]"
                                                    :value="payment"
                                                    :id="payment.method"
                                                    @change="handlePaymentMethodChange(payment)"
                                                    v-model="selectedPaymentMethod"
                                                    class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                                />
                                                <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                            </div>

                                            <img
                                                v-if="payment.image"
                                                class="max-h-6 max-w-6 mt-1"
                                                :src="payment.image"
                                                width="30"
                                                height="30"
                                                :alt="payment.method_title"
                                                :title="payment.method_title"
                                            />

                                            <p class="text-base leading-0 mt-1 text-dark-gray font-bold">
                                                @{{ payment.method_title }}
                                            </p>
                                        </label>
                                    </div>
                                    <div v-if="payment.method == 'stripe' && selectedPaymentMethod.method == 'stripe'" class="p-5">
                                        <div id="express-checkout-element"></div>
                                        <div id="payment-element"></div>

                                        <div v-if="stripePaymentStatus" class="mt-3">
                                            <div v-if="stripePaymentStatus === 'processing'" class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-md">
                                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                                <span class="text-sm text-blue-700">@lang('shop::app.checkout.onepage.payment.processing')</span>
                                            </div>
                                            <div v-else-if="stripePaymentStatus === 'error'" class="p-3 bg-red-50 border border-red-200 rounded-md">
                                                <span class="text-sm text-red-700">@{{ stripeErrorMessage }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button @click="proceedToCheckout()" :disabled="isStripeProcessing" class="w-full capitalize text-base leading-22px bg-secondary hover:bg-black duration-300 text-white py-3 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed">
                                <span v-if="isStripeProcessing" class="flex items-center justify-center">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    @lang('shop::app.checkout.cart.summary.processing')
                                </span>
                                <span v-else>
                                    @lang('shop::app.checkout.cart.summary.pay')
                                </span>
                            </button>
                        </div>
                    </section>
                </div>
            </template>
        </script>

        <script type="module">
            app.component('v-checkout', {
                template: '#v-checkout-template',

                data() {
                    return {
                        cart: null,

                        termsOfService: Boolean("{{ (Auth::check() && Auth::user()->terms_conditions) ? true : false }}"),
                        isGift: false,
                        promotionEmails: Boolean("{{ (Auth::check() && Auth::user()->subscribed_to_news_letter == 1) ? true : false }}"),
                        orderComments: '',

                        customerSavedAddresses: {
                            'billing': [],
                            'shipping': [],
                        },

                        useBillingAddressForShipping: true,

                        activeAddressForm: 'billing',

                        selectedAddressForEdit: null,

                        save_address: true,

                        selectedAddresses: {
                            billing_address_id: null,

                            shipping_address_id: null,
                        },

                        selectedCountry: '',

                        countries: [],

                        states: null,

                        displayTax: {
                            prices: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_prices') }}",
                            subtotal: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_subtotal') }}",
                            shipping: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_shipping_amount') }}",
                        },

                        shippingMethods: [],

                        paymentMethods: [],

                        country: "{{ old('country') }}",

                        state: "{{ old('state') }}",

                        isStoring: false,

                        selectedAddress: null,

                        complementaryProducts: [],

                        addresses: [],

                        addressForm: {
                            id: 0,
                            company_name: '',
                            first_name: '',
                            last_name: '',
                            email: '',
                            vat_id: '',
                            address: [''],
                            country: '',
                            state: '',
                            city: '',
                            postcode: '',
                            phone: '',
                            default_address: 1
                        },

                        countryStates: @json(core()->groupedStatesByCountries()),

                        isPostcodeHidden: false,

                        stripe: null,

                        elements: null,

                        clientSecret: null,

                        selectedPaymentMethod: null,

                        selectedShippingMethod: null,

                        stripePaymentStatus: null,

                        stripeErrorMessage: '',

                        expressCheckoutElement: null,

                        paymentElement: null,

                        isStripeProcessing: false
                    }
                },

                created() {
                    if (this.cart && this.cart.billing_address) {
                        this.useBillingAddressForShipping = this.cart.billing_address.use_for_shipping;

                        if (this.cart.billing_address.country) {
                            this.selectedCountry = this.cart.billing_address.country;
                        }
                    }
                },

                async mounted() {
                    this.getCart();
                    this.getCountries();
                    this.getStates();
                    this.getCustomerSavedAddresses();
                    this.checkStripePaymentStatus();

                    this.$emitter.on('update-mini-cart', (cart) => {
                        if (cart) {
                            this.cart = cart;
                        } else {
                            window.location.href = '{{ route('shop.checkout.cart.index', app()->getLocale()) }}';
                        }
                    });

                    this.$emitter.on('complementary-product-updated', () => {
                        this.getComplementaryProducts();
                    });

                    this.$axios.get("{{ route('shop.checkout.onepage.summary', app()->getLocale()) }}")
                        .then(async response => {
                            if (response.data.data) {
                                this.cart = response.data.data;

                                if (this.cart.shipping_address) {
                                    this.selectedAddress = this.cart.shipping_address;
                                } else {
                                    this.selectedAddress = this.addresses.find(addr => addr.default_address) || this.addresses[0];
                                }

                                if(this.selectedAddress) {
                                    await this.getShippingMethods();
                                    if (this.cart.shipping_method) {
                                        this.saveShippingMethod(this.cart.shipping_method);
                                    }
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching cart:', error);
                        });

                    this.getComplementaryProducts();
                },

                watch: {
                    selectedCountry(newCountry) {
                        this.addressForm.country = newCountry;
                        this.isPostcodeHidden = (newCountry == 'AE');
                    },
                },

                computed: {
                    haveStates() {
                        return this.states && this.selectedCountry && this.states[this.selectedCountry]?.length > 0;
                    },
                },

                methods: {
                    openAddressForm(type) {
                        this.activeAddressForm = type;

                        this.addressForm = {
                            id: 0,
                            company_name: '',
                            first_name: '',
                            last_name: '',
                            email: '',
                            address: [''],
                            country: '',
                            state: '',
                            city: '',
                            postcode: '',
                            phone: '',
                            default_address: 1
                        };

                        this.getCustomerSavedAddresses();
                        if (this.$refs.addressDrawer) {
                            this.$refs.addressDrawer.close();
                        }

                        if (this.$refs.createAddressDrawer) {
                            this.$refs.createAddressDrawer.open();
                        }
                    },

                    getCountries() {
                        this.$axios.get("{{ route('shop.api.core.countries', app()->getLocale()) }}")
                            .then(response => {
                                this.countries = response.data.data;
                            })
                            .catch(() => {});
                    },

                    getStates() {
                        this.$axios.get("{{ route('shop.api.core.states', app()->getLocale()) }}")
                            .then(response => {
                                this.states = response.data.data;
                            })
                            .catch(() => {});
                    },

                    initializeAddresses(type, addresses) {
                        this.customerSavedAddresses[type] = addresses;

                        let cartAddress = this.cart[type + '_address'];

                        if (! cartAddress) {
                            addresses.forEach(address => {
                                if (address.default_address) {
                                    this.selectedAddresses[type + '_address_id'] = address.id;

                                    if (this.activeAddressForm === type) {
                                        this.selectedAddress = address;
                                    }
                                }
                            });

                            return addresses;
                        }

                        if (cartAddress.parent_address_id) {
                            addresses.forEach(address => {
                                if (address.id == cartAddress.parent_address_id) {
                                    this.selectedAddresses[type + '_address_id'] = address.id;

                                    if (this.activeAddressForm === type) {
                                        this.selectedAddress = address;
                                    }
                                }
                            });
                        } else {
                            this.selectedAddresses[type + '_address_id'] = cartAddress.id;

                            if (this.activeAddressForm === type) {
                                this.selectedAddress = cartAddress;
                            }

                            addresses.unshift(cartAddress);
                        }

                        return addresses;
                    },

                    getCustomerSavedAddresses() {
                        if('{{ auth()->guard("customer")->check() }}') {
                            this.$axios.get('{{ route('shop.api.customers.account.addresses.index', app()->getLocale()) }}')
                                .then(response => {
                                    this.initializeAddresses('billing', structuredClone(response.data.data));

                                    this.initializeAddresses('shipping', structuredClone(response.data.data));

                                    if (!this.customerSavedAddresses.billing.length) {
                                        this.activeAddressForm = 'billing';
                                    }

                                    if (response.data.data.length > 0) {
                                        this.addresses = response.data.data;
                                        this.getShippingMethods();
                                    }
                                })
                                .catch((error) => {
                                    console.error(error);
                                });
                        }
                    },

                    saveAddress() {
                        this.isStoring = true;

                        this.$axios.post('{{ route('shop.api.customers.account.addresses.store', app()->getLocale()) }}', this.addressForm)
                            .then(response => {
                                this.isStoring = false;

                                if (response.data.message) {
                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                                    const address = response.data.data;
                                    this.addresses.push(address);
                                    setTimeout(() => {
                                        this.selectedAddress = address;
                                        this.addressForm = {
                                            company_name: '',
                                            first_name: '',
                                            last_name: '',
                                            email: '',
                                            vat_id: '',
                                            address: [''],
                                            country: '',
                                            state: '',
                                            city: '',
                                            postcode: '',
                                            phone: '',
                                            default_address: 1
                                        };
                                    }, 300);
                                }

                                this.$refs.createAddressDrawer.close();
                                this.getCustomerSavedAddresses();
                                this.$refs.addressDrawer.open();
                            })
                            .catch(error => {
                                this.isStoring = false;

                                if (error.response.status == 422) {
                                    this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                                }
                            });
                    },

                    saveGuestAddress() {
                        this.isStoring = true;

                        let payload = {};
                        payload[this.activeAddressForm] = {
                            ...this.addressForm,
                            default_address: 0,
                            use_for_shipping: 1
                        };

                        this.storeAddress(payload, true);
                    },

                    storeAddress(payload, isGuest = false) {
                        this.$axios.post('{{ route('shop.checkout.onepage.addresses.store', app()->getLocale()) }}', payload)
                            .then(response => {
                                this.isStoring = false;

                                if(isGuest) {
                                    this.$refs.createAddressDrawer.close();
                                    this.addressForm = {
                                        company_name: '',
                                        first_name: '',
                                        last_name: '',
                                        email: '',
                                        vat_id: '',
                                        address: [''],
                                        country: '',
                                        state: '',
                                        city: '',
                                        postcode: '',
                                        phone: '',
                                        default_address: 1
                                    };
                                    if(response.data.cart.shipping_address) {
                                        this.selectedAddress = response.data.cart.shipping_address;
                                    }
                                } else if(this.$refs.addressDrawer && this.$refs.addressDrawer.isOpen) {
                                    this.$refs.addressDrawer.close();
                                }

                                this.$emitter.emit('add-flash', { type: 'success', message: '{{ trans('shop::app.checkout.onepage.address-selected') }}' });

                                this.getCart();
                                if (this.cart.have_stockable_items) {
                                    this.getShippingMethods();
                                }
                            })
                            .catch(error => {
                                this.isStoring = false;

                                if (error.response.status == 422) {
                                    this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                                }
                            });
                    },

                    selectAddress(address, type) {
                        this.selectedAddresses[type + '_address_id'] = address.id;
                        this.selectedAddress = address;

                        let payload = {};
                        payload[type] = {
                            ...address,
                            default_address: 0
                        };

                        if (type === 'billing') {
                            payload.billing.use_for_shipping = this.useBillingAddressForShipping;

                            if (!this.useBillingAddressForShipping && this.selectedAddresses.shipping_address_id) {
                                const shippingAddress = this.customerSavedAddresses.shipping.find(
                                    addr => addr.id === this.selectedAddresses.shipping_address_id
                                );

                                if (shippingAddress) {
                                    this.selectedAddress = shippingAddress;
                                    payload.shipping = {
                                        ...shippingAddress,
                                        default_address: 0
                                    };
                                }
                            }
                        } else if (type === 'shipping') {
                            this.useBillingAddressForShipping = false;

                            if (this.selectedAddresses.billing_address_id) {
                                const billingAddress = this.customerSavedAddresses.billing.find(
                                    addr => addr.id === this.selectedAddresses.billing_address_id
                                );

                                if (billingAddress) {
                                    payload.billing = {
                                        ...billingAddress,
                                        default_address: 0,
                                        use_for_shipping: false
                                    };
                                }
                            }
                        }

                        this.isStoring = true;

                        this.storeAddress(payload);
                    },

                    getShippingMethods() {
                        this.$axios.get("{{ route('shop.checkout.onepage.get-shipping-methods', app()->getLocale()) }}")
                            .then(response => {
                                this.shippingMethods = Object.values(response.data.data.shippingMethods);

                                if (!this.selectedAddress && (!this.shippingMethods || this.shippingMethods.length === 0)) {
                                    this.$emitter.emit('add-flash', {
                                        type: 'warning',
                                        message: '{{ trans('shop::app.checkout.onepage.no-shipping-methods-available') }}'
                                    });
                                } else if (this.shippingMethods.length > 0 && this.shippingMethods[0].rates.length > 0) {
                                    this.saveShippingMethod(this.shippingMethods[0]?.rates[0].method);
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching shipping methods:', error);
                            });
                    },

                    saveShippingMethod(selectedMethod) {
                        this.$axios.post("{{ route('shop.checkout.onepage.shipping_methods.store', app()->getLocale()) }}", {
                                shipping_method: selectedMethod,
                            })
                            .then(response => {
                                if (response.data.redirect_url) {
                                    window.location.href = response.data.redirect_url;
                                } else {
                                    this.selectedShippingMethod = selectedMethod;
                                    this.paymentMethods = Object.values(response.data.payment_methods);

                                    if(this.paymentMethods && this.selectedPaymentMethod?.method != this.paymentMethods[0]?.method) {
                                        this.selectedPaymentMethod = this.paymentMethods ? this.paymentMethods[0] : null;
                                        this.handlePaymentMethodChange(this.selectedPaymentMethod);
                                    }

                                    this.getCart();
                                }
                            })
                            .catch(error => {
                                if (error.response.data.redirect_url) {
                                    return false;
                                }

                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: error.response.data.message || '{{ trans('shop::app.checkout.onepage.error-saving-shipping-method') }}'
                                });
                            });
                    },

                    getCart() {
                        this.$axios.get("{{ route('shop.checkout.onepage.summary', app()->getLocale()) }}")
                            .then(response => {
                                this.cart = response.data.data;
                            })
                            .catch(error => {});
                    },

                    update(quantity, item) {
                        this.isModifying = true;

                        let qty = {};

                        qty[item.id] = quantity;

                        this.$axios.put('{{ route('shop.api.checkout.cart.update', app()->getLocale()) }}', { qty })
                            .then(response => {
                                if (response.data.message) {
                                    this.cart = response.data.data;
                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                    this.$emitter.emit('update-mini-cart', this.cart);
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isModifying = false;
                            }).catch(error => this.isModifying = false);
                    },

                    applyCoupon(params, { resetForm }) {
                        this.isStoring = true;

                        this.$axios.post("{{ route('shop.api.checkout.cart.coupon.apply', app()->getLocale()) }}", params)
                            .then((response) => {
                                this.isStoring = false;

                                this.$emit('coupon-applied');

                                this.getCart();

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                resetForm();
                            })
                            .catch((error) => {
                                this.isStoring = false;

                                if ([400, 422].includes(error.response.request.status)) {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.message });

                                    resetForm();

                                    return;
                                }

                                this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                            });
                    },

                    destroyCoupon() {
                        this.$axios.delete("{{ route('shop.api.checkout.cart.coupon.remove', app()->getLocale()) }}", {
                                '_token': "{{ csrf_token() }}"
                            })
                            .then((response) => {
                                this.$emit('coupon-removed');

                                this.getCart();

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                            })
                            .catch(error => console.error(error));
                    },

                    getComplementaryProducts() {
                        this.$axios.get('{{ route('shop.api.checkout.get-cart-complementary-products', app()->getLocale()) }}')
                            .then(response => {
                                this.complementaryProducts = response.data.data;
                            })
                            .catch(error => {
                                console.error("Failed to load complementary products", error);
                            });
                    },

                    handlePaymentMethodChange(payment) {
                        this.resetStripeState();

                        if (payment.method == 'stripe') {
                            this.$nextTick(() => {
                                setTimeout(() => {
                                    this.initStripeCheckoutSession();
                                }, 200);
                            });
                        }
                    },

                    initStripeCheckoutSession() {
                        this.stripeErrorMessage = '';
                        this.stripePaymentStatus = null;

                        const publishableKey = '{{ core()->getConfigData('sales.payment_methods.stripe.stripe_publishable_key') }}';

                        if (!publishableKey) {
                            this.handleStripeError('Stripe publishable key not configured');
                            return;
                        }

                        try {
                            this.stripe = Stripe(publishableKey);

                            this.$axios.post('{{ route('shop.api.payment.create-payment-checkout-session', app()->getLocale()) }}')
                                .then(response => {
                                    this.clientSecret = response.data.clientSecret;

                                    setTimeout(() => {
                                        this.setupStripeElements();
                                    }, 300);
                                })
                                .catch(error => {
                                    this.handleStripeError(error.response?.data?.message || 'Failed to initialize payment');
                                });
                        } catch (error) {
                            this.handleStripeError('Failed to initialize Stripe: ' + error.message);
                        }
                    },

                    setupStripeElements() {
                        if (!this.stripe || !this.clientSecret) {
                            this.handleStripeError('Payment session not initialized');
                            return;
                        }

                        try {
                            this.elements = this.stripe.elements({
                                clientSecret: this.clientSecret,
                                appearance: {
                                    theme: 'stripe',
                                    variables: {
                                        colorPrimary: '#0570de',
                                        colorBackground: '#ffffff',
                                        colorText: '#30313d',
                                        colorDanger: '#df1b41',
                                        fontFamily: 'Ideal Sans, system-ui, sans-serif',
                                        spacingUnit: '2px',
                                        borderRadius: '4px',
                                    }
                                }
                            });

                            this.setupExpressCheckout();
                            this.setupPaymentElement();
                        } catch (error) {
                            console.error('Error setting up Stripe elements:', error);
                            this.handleStripeError('Failed to setup payment form');
                        }
                    },

                    setupExpressCheckout() {
                        try {
                            const expressCheckoutContainer = document.getElementById('express-checkout-element');
                            if (!expressCheckoutContainer) {
                                setTimeout(() => this.setupExpressCheckout(), 500);
                                return;
                            }

                            this.expressCheckoutElement = this.elements.create('expressCheckout', {
                                buttonHeight: 40,
                                buttonTheme: {
                                    applePay: 'black',
                                    googlePay: 'black',
                                    paypal: 'silver',
                                },
                                buttonType: {
                                    applePay: 'buy',
                                    googlePay: 'pay',
                                    paypal: 'pay',
                                },
                            });

                            this.expressCheckoutElement.on('click', (event) => {
                                this.stripePaymentStatus = 'processing';
                                this.stripeErrorMessage = '';

                                const billingDetails = this.getBillingDetails();
                                const shippingDetails = this.getShippingDetails();

                                event.resolve({
                                    billingDetails: billingDetails,
                                    shippingAddress: shippingDetails
                                });
                            });

                            this.expressCheckoutElement.on('confirm', async (event) => {
                                try {
                                    await this.handleExpressCheckoutConfirm(event);
                                } catch (error) {
                                    this.handleStripeError('Payment confirmation failed: ' + error.message);
                                }
                            });

                            this.expressCheckoutElement.on('cancel', () => {
                                this.stripePaymentStatus = null;
                                this.stripeErrorMessage = '';
                            });

                            this.expressCheckoutElement.on('shippingaddresschange', (event) => {
                                event.resolve({
                                    shippingRates: [{
                                        id: 'standard-shipping',
                                        displayName: 'Standard Shipping',
                                        amount: Math.round((this.cart?.shipping_amount || 0) * 100),
                                        detail: 'Delivered in 3-5 business days'
                                    }]
                                });
                            });

                            this.expressCheckoutElement.on('shippingratechange', (event) => {
                                event.resolve({});
                            });

                            this.expressCheckoutElement.mount('#express-checkout-element');
                        } catch (error) {
                            console.error('Error setting up express checkout:', error);
                        }
                    },

                    setupPaymentElement() {
                        try {
                            const paymentContainer = document.getElementById('payment-element');
                            if (!paymentContainer) {
                                setTimeout(() => this.setupPaymentElement(), 500);
                                return;
                            }

                            this.paymentElement = this.elements.create('payment', {
                                layout: 'tabs',
                                fields: {
                                    billingDetails: 'auto'
                                }
                            });

                            this.paymentElement.on('ready', () => {
                                this.stripePaymentStatus = null;
                            });

                            this.paymentElement.on('change', (event) => {
                                if (event.error) {
                                    this.handleStripeError(event.error.message);
                                } else {
                                    this.stripePaymentStatus = null;
                                    this.stripeErrorMessage = '';
                                }
                            });

                            this.paymentElement.mount('#payment-element');
                        } catch (error) {
                            console.error('Error setting up payment element:', error);
                            this.handleStripeError('Failed to setup payment form');
                        }
                    },

                    getBillingDetails() {
                        const billingAddress = this.cart?.billing_address || this.selectedAddress;

                        if (!billingAddress) {
                            return {
                                name: '',
                                email: '',
                                phone: '',
                                address: {
                                    line1: '',
                                    city: '',
                                    state: '',
                                    postal_code: '',
                                    country: 'AE'
                                }
                            };
                        }

                        const details = {
                            name: `${billingAddress.first_name || ''} ${billingAddress.last_name || ''}`.trim(),
                            email: billingAddress.email || '',
                            phone: billingAddress.phone || '',
                            address: {
                                line1: billingAddress.address?.[0] || '',
                                line2: billingAddress.address?.[1] || '',
                                city: billingAddress.city || '',
                                state: billingAddress.state || '',
                                postal_code: billingAddress.postcode || '',
                                country: billingAddress.country || 'AE'
                            }
                        };
                        return details;
                    },

                    getShippingDetails() {
                        const shippingAddress = this.cart?.shipping_address || this.selectedAddress;
                        if (!shippingAddress) {
                            return {
                                name: '',
                                address: {
                                    line1: '',
                                    city: '',
                                    state: '',
                                    postal_code: '',
                                    country: 'AE'
                                }
                            };
                        }

                        const details = {
                            name: `${shippingAddress.first_name || ''} ${shippingAddress.last_name || ''}`.trim(),
                            address: {
                                line1: shippingAddress.address?.[0] || '',
                                line2: shippingAddress.address?.[1] || '',
                                city: shippingAddress.city || '',
                                state: shippingAddress.state || '',
                                postal_code: shippingAddress.postcode || '',
                                country: shippingAddress.country || 'AE'
                            }
                        };
                        return details;
                    },

                    async handleExpressCheckoutConfirm(event) {
                        try {
                            this.stripePaymentStatus = 'processing';

                            await this.storeCheckoutData();

                            if (event.expressPaymentType === 'link') {
                                const { error } = await this.stripe.confirmPayment({
                                    elements: this.elements,
                                    confirmParams: {
                                        return_url: "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}",
                                    },
                                    redirect: 'if_required'
                                });

                                if (error) {
                                    this.handleStripeError(error.message);
                                } else {
                                    await this.storeCheckoutDataInSession();
                                    window.location.href = "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}";
                                }
                            } else {
                                const { error, paymentIntent } = await this.stripe.confirmPayment({
                                    elements: this.elements,
                                    confirmParams: {
                                        return_url: "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}",
                                    },
                                    redirect: 'if_required'
                                });

                                if (error) {
                                    this.handleStripeError(error.message);
                                } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                                    await this.storeCheckoutDataInSession();
                                    window.location.href = "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}";
                                } else {
                                    this.handleStripeError('Payment could not be completed. Please try again.');
                                }
                            }
                        } catch (error) {
                            console.error('Express checkout error:', error);
                            this.handleStripeError('Payment failed. Please try again.');
                        }
                    },

                    async storeCheckoutData() {
                        const payload = {
                            terms_of_service: this.termsOfService ? 1 : 0,
                            is_gift: this.isGift ? 1 : 0,
                            promotion_emails: this.promotionEmails ? 1 : 0,
                            order_comments: this.orderComments
                        };

                        try {
                            await this.$axios.post("{{ route('shop.checkout.onepage.payment_methods.store', app()->getLocale()) }}", {
                                payment: this.selectedPaymentMethod
                            });
                        } catch (error) {
                            console.error('Failed to store payment method:', error);
                            throw error;
                        }
                    },

                    handleStripeError(message) {
                        this.stripePaymentStatus = 'error';
                        this.stripeErrorMessage = this.getStripeErrorMessage(message);
                        this.isStripeProcessing = false;

                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: this.stripeErrorMessage
                        });
                    },

                    getStripeErrorMessage(error) {
                        const errorMessages = {
                            'card_declined': 'Your card was declined. Please try a different payment method.',
                            'expired_card': 'Your card has expired. Please use a different card.',
                            'incorrect_cvc': 'Your card\'s security code is incorrect.',
                            'processing_error': 'An error occurred while processing your card. Please try again.',
                            'incorrect_number': 'Your card number is incorrect.',
                            'incomplete_number': 'Your card number is incomplete.',
                            'incomplete_cvc': 'Your card\'s security code is incomplete.',
                            'incomplete_expiry': 'Your card\'s expiration date is incomplete.',
                            'invalid_expiry_month': 'Your card\'s expiration month is invalid.',
                            'invalid_expiry_year': 'Your card\'s expiration year is invalid.',
                            'invalid_cvc': 'Your card\'s security code is invalid.',
                        };

                        const errorType = typeof error === 'string' ? error.toLowerCase() : '';

                        for (const [key, message] of Object.entries(errorMessages)) {
                            if (errorType.includes(key)) {
                                return message;
                            }
                        }

                        return error || 'An unexpected error occurred. Please try again.';
                    },

                    resetStripeState() {
                        this.stripePaymentStatus = null;
                        this.stripeErrorMessage = '';
                        this.isStripeProcessing = false;

                        if (this.expressCheckoutElement) {
                            this.expressCheckoutElement.unmount();
                            this.expressCheckoutElement = null;
                        }
                        if (this.paymentElement) {
                            this.paymentElement.unmount();
                            this.paymentElement = null;
                        }

                        const expressContainer = document.getElementById('express-checkout-element');
                        const paymentContainer = document.getElementById('payment-element');

                        if (expressContainer) {
                            expressContainer.innerHTML = '';
                        }
                        if (paymentContainer) {
                            paymentContainer.innerHTML = '';
                        }
                    },

                    proceedToCheckout() {
                        if (!this.selectedAddress || this.selectedAddress) {
                            if (this.cart && !this.cart.shipping_address) {
                                this.$emitter.emit('add-flash', { type: 'error', message: '{{ trans('shop::app.checkout.onepage.select-address') }}' });
                                return;
                            }
                        }

                        if (!this.selectedShippingMethod) {
                            this.$emitter.emit('add-flash', { type: 'error', message: '{{ trans('shop::app.checkout.onepage.select-shipping-method') }}' });
                            return;
                        }

                        if (!this.selectedPaymentMethod) {
                            this.$emitter.emit('add-flash', { type: 'error', message: '{{ trans('shop::app.checkout.onepage.select-payment-method') }}' });
                            return;
                        }

                        if (!this.termsOfService) {
                            this.$emitter.emit('add-flash', { type: 'error', message: '{{ trans('shop::app.checkout.onepage.terms-and-conditions-required') }}' });
                            return;
                        }

                        const payload = {
                            terms_of_service: this.termsOfService ? 1 : 0,
                            is_gift: this.isGift ? 1 : 0,
                            promotion_emails: this.promotionEmails ? 1 : 0,
                            order_comments: this.orderComments
                        };

                        this.$axios.post("{{ route('shop.checkout.onepage.payment_methods.store', app()->getLocale()) }}", { payment: this.selectedPaymentMethod })
                            .then(async response => {
                                if (this.selectedPaymentMethod.method == 'stripe') {
                                    await this.processStripePayment();
                                } else {
                                    this.$axios.post("{{ route('shop.checkout.onepage.orders.store', app()->getLocale()) }}", payload)
                                        .then(response => {
                                            if (response.data.data.error) {
                                                this.$emitter.emit('add-flash', { type: 'error', message: '{{ trans('shop::app.checkout.onepage.terms-and-conditions-required') }}' });
                                                return;
                                            }
                                            window.location.href = response.data.data.redirect_url;
                                        })
                                        .catch(error => {
                                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                                        });
                                }
                            })
                            .catch(error => {
                                console.error(error);
                                this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });
                            });

                    },

                    async processStripePayment() {
                        if (!this.stripe || !this.elements) {
                            this.handleStripeError('Payment system not initialized. Please refresh and try again.');
                            return;
                        }

                        try {
                            this.isStripeProcessing = true;
                            this.stripePaymentStatus = 'processing';

                            const { error, paymentIntent } = await this.stripe.confirmPayment({
                                elements: this.elements,
                                confirmParams: {
                                    return_url: "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}",
                                },
                                redirect: 'if_required'
                            });

                            if (error) {
                                this.handleStripeError(error.message);

                                this.$axios.post('{{ route('shop.api.payment.stripe-transaction-log', app()->getLocale()) }}', {
                                    message: error.message,
                                    total_amount: this.cart.grand_total,
                                    error_type: error.type || 'unknown'
                                }).catch(logError => {
                                    console.error('Failed to log Stripe error:', logError);
                                });
                            } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                                this.stripePaymentStatus = 'success';

                                await this.storeCheckoutDataInSession();

                                window.location.href = "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}";
                            } else {
                                this.handleStripeError('Payment could not be completed. Please try again.');
                            }
                        } catch (error) {
                            console.error('Stripe payment processing error:', error);
                            this.handleStripeError('Payment processing failed. Please try again.');
                        } finally {
                            this.isStripeProcessing = false;
                        }
                    },

                    async storeCheckoutDataInSession() {
                        const payload = {
                            terms_of_service: this.termsOfService ? 1 : 0,
                            is_gift: this.isGift ? 1 : 0,
                            promotion_emails: this.promotionEmails ? 1 : 0,
                            order_comments: this.orderComments
                        };

                        try {
                            await this.$axios.post('{{ route('shop.api.payment.store-checkout-data', app()->getLocale()) }}', payload);
                        } catch (error) {
                            console.error('Failed to store checkout data:', error);
                        }
                    },

                    checkStripePaymentStatus() {
                        const urlParams = new URLSearchParams(window.location.search);
                        const paymentIntent = urlParams.get('payment_intent');
                        const paymentIntentClientSecret = urlParams.get('payment_intent_client_secret');

                        if (paymentIntent && paymentIntentClientSecret) {
                            if (this.stripe) {
                                this.stripe.retrievePaymentIntent(paymentIntentClientSecret)
                                    .then(({ paymentIntent }) => {
                                        if (paymentIntent.status === 'succeeded') {
                                            this.storeCheckoutDataInSession().then(() => {
                                                window.location.href = "{{ route('shop.checkout.onepage.payment-complete', app()->getLocale()) }}";
                                            });
                                        } else {
                                            this.handleStripeError('Payment was not completed successfully.');
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error retrieving payment intent:', error);
                                    });
                            }
                        }
                    }

                },
            });
        </script>
    @endPushOnce
</x-shop::layouts>
