<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="@lang('shop::app.checkout.cart.index.cart')"/>

    <meta name="keywords" content="@lang('shop::app.checkout.cart.index.cart')"/>
@endPush

<x-shop::layouts
    :has-header="true"
    :has-feature="false"
    :has-footer="true"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.checkout.cart.index.cart')
    </x-slot>

    <v-cart ref="vCart">
        <!-- Cart Shimmer Effect -->
        <x-shop::shimmer.checkout.cart :count="3" />
    </v-cart>

    @pushOnce('scripts')
        <script type="text/x-template" id="v-cart-template">
            <template v-if="isLoading">
                <x-shop::shimmer.checkout.cart :count="3" />
            </template>

            <template v-else-if="cart?.items?.length">
                <!--Section Heading-->
                <div class="py-28 bg-primary-dark text-secondary font-playfair text-5xl leading-[60px] text-center">
                    @lang('shop::app.checkout.cart.index.your-cart')
                </div>

                <!--Products List-->
                <section class="sm:pt-28 relative max-w-[1640px] mx-auto mb-[30px] px-5 sm:px-10">
                    <div class="w-full max-2xl:px-5">
                        <!--item Headings-->
                        <div class="hidden lg:grid grid-cols-1 lg:grid-cols-2 py-[20px] ps-[70px] border-b border-light-gray">
                            <p class="text-lg leading-normal text-secondary font-bold flex items-center flex-col min-[550px]:flex-row gap-5 min-[550px]:gap-6 w-full">
                                @lang('shop::app.checkout.cart.index.item')
                            </p>

                            <p class="text-lg leading-normal text-secondary font-bold flex items-center flex-col min-[550px]:flex-row w-full max-xl:max-w-xl max-xl:mx-auto gap-2">
                                <span class="text-left w-full">@lang('shop::app.checkout.cart.index.price')</span>
                                <span class="text-left w-full">@lang('shop::app.checkout.cart.index.quantity')</span>
                                <span class="text-right w-full">@lang('shop::app.checkout.cart.index.total')</span>
                            </p>
                        </div>
                        <div v-for="item in cart?.items" class="grid grid-cols-1 lg:grid-cols-2 gap-10 min-[550px]:gap-6 border-b border-light-gray py-[43px]">
                            <div class="cursor-pointer flex items-center flex-col min-[550px]:flex-row gap-5 min-[550px]:gap-6">
                                <div class="flex flex-col items-center group">
                                    {!! view_render_event('bagisto.shop.checkout.cart.remove_button.before') !!}

                                    <span role="button" tabindex="0" @click="removeItem(item.id)" class="flex items-center justify-center text-white mb-[10px] h-[18px] w-[18px] bg-light-gray group-hover:bg-light-gray-2 rounded duration-200">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" stroke="currentColor"
                                            stroke-width="3" stroke-linecap="square" stroke-linejoin="miter" viewBox="0 0 24 24">
                                            <line x1="18" y1="6" x2="6" y2="18" />
                                            <line x1="6" y1="6" x2="18" y2="18" />
                                        </svg>
                                    </span>

                                    {!! view_render_event('bagisto.shop.checkout.cart.remove_button.after') !!}

                                    <span class="text-light-gray-2 group-hover:text-widian-red text-xs capitalize leading-lg duration-300">
                                        @lang('shop::app.checkout.cart.index.remove')
                                    </span>
                                </div>

                                <div>
                                    {!! view_render_event('bagisto.shop.checkout.cart.item_image.before') !!}

                                    <!-- Cart Item Image -->
                                    <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', item.product_url_key)">
                                        <x-shop::media.images.lazy
                                            class="xl:min-w-[213px] xl:max-w-[213px] lg:min-w-[190px] lg:max-w-[190px] sm:min-w-[170px] sm:max-w-[170px] min-w-[250px] max-w-[250px] object-cover"
                                            ::src="item.base_image.original_image_url"
                                            ::alt="item.name"
                                            ::key="item.id"
                                            ::index="item.id"
                                        />
                                    </a>

                                    {!! view_render_event('bagisto.shop.checkout.cart.item_image.after') !!}
                                </div>

                                <div class="w-full lg:max-w-sm sm:text-left text-center">
                                    <h5 class="font-regular text-lg leading-6 text-dark-gray mb-1">
                                        @{{ item.categories && item.categories.length > 0 ? item.categories[0].name : '' }}
                                    </h5>

                                    {!! view_render_event('bagisto.shop.checkout.cart.item_name.before') !!}

                                    <a class="uppercase font-bold font-playfair text-lg leading-8 text-secondary" :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', item.product_url_key)">
                                        @{{ item.name }}
                                    </a>

                                    {!! view_render_event('bagisto.shop.checkout.cart.item_name.after') !!}
                                </div>
                            </div>

                            <div class="flex items-center max-lg:flex-col sm:gap-5 gap-3">
                                <div class="flex items-center justify-between w-full">
                                    <p class="lg:hidden text-lg leading-normal text-secondary font-bold">
                                        @lang('shop::app.checkout.cart.index.price')
                                    </p>

                                    <template v-if="displayTax.prices == 'including_tax'">
                                        <h6 class="text-nowrap ps-[30px] sm:text-[22px] text-lg leading-7 max-lg:text-right text-left text-dark-gray">
                                            @{{ item.formatted_price_incl_tax }}
                                        </h6>
                                    </template>

                                    <template v-else-if="displayTax.prices == 'both'">
                                        <h6 class="flex flex-col text-nowrap ps-[30px] sm:text-[22px] text-lg leading-7 max-lg:text-right text-left text-dark-gray">
                                            @{{ item.formatted_price_incl_tax }}

                                            <span class="text-xs font-normal">
                                                @lang('shop::app.checkout.cart.index.excl-tax')

                                                <span class="font-medium">@{{ item.formatted_price }}</span>
                                            </span>
                                        </h6>
                                    </template>

                                    <template v-else>
                                        <h6 class="text-nowrap ps-[30px] sm:text-[22px] text-lg leading-7 max-lg:text-right text-left text-dark-gray">
                                            @{{ item.formatted_price }}
                                        </h6>
                                    </template>
                                </div>

                                <div class="w-full flex items-center justify-between">
                                    <p class="lg:hidden text-lg leading-normal text-secondary font-bold">
                                        @lang('shop::app.checkout.cart.index.quantity')
                                    </p>

                                    <div class="text-dark-gray flex items-start justify-start border border-[#CCC] w-fit">
                                        {!! view_render_event('bagisto.shop.checkout.cart.quantity_changer.before') !!}

                                        <x-shop::quantity-changer
                                            class="text-dark-gray flex justify-start"
                                            name="quantity"
                                            ::max="item?.stock_qty"
                                            ::value="item?.quantity"
                                            @change="update($event, item)"
                                        />

                                        {!! view_render_event('bagisto.shop.checkout.cart.quantity_changer.after') !!}
                                    </div>
                                </div>

                                <div class="max-lg:flex items-center justify-between w-full">
                                    <p class="lg:hidden text-lg leading-normal text-secondary font-bold">
                                        @lang('shop::app.checkout.cart.index.total')
                                    </p>

                                    {!! view_render_event('bagisto.shop.checkout.cart.total.before') !!}

                                    <template v-if="displayTax.prices == 'including_tax'">
                                        <h6 class="text-nowrap text-right sm:text-[22px] text-lg leading-7 text-dark-gray">
                                            @{{ item.formatted_total_incl_tax }}
                                        </h6>
                                    </template>

                                    <template v-else-if="displayTax.prices == 'both'">
                                        <h6 class="flex flex-col text-nowrap text-right sm:text-[22px] text-lg leading-7 text-dark-gray">
                                            @{{ item.formatted_total_incl_tax }}

                                            <span class="text-xs font-normal">
                                                @lang('shop::app.checkout.cart.index.excl-tax')

                                                <span class="font-medium">@{{ item.formatted_total }}</span>
                                            </span>
                                        </h6>
                                    </template>

                                    <template v-else>
                                        <h6 class="text-nowrap text-right sm:text-[22px] text-lg leading-7 text-dark-gray">
                                            @{{ item.formatted_total }}
                                        </h6>
                                    </template>

                                    {!! view_render_event('bagisto.shop.checkout.cart.total.after') !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- complimentary samples -->
                <section class="max-w-[1640px] mx-auto px-5 sm:px-10 py-[50px] bg-primary-dark mb-5">
                    <h4 class="text-dark-gray font-bold text-base leadinding-[22px] mb-[30px]">
                        @lang('shop::app.checkout.cart.index.complimentary-samples')
                    </h4>

                    <!-- Stepper -->
                    <ul class="flex items-center w-full mb-[30px]">
                        <li v-for="i in 7" :key="i" :class="(cart?.complementary_products.length - 1) >= i ? 'after:bg-secondary' : 'after:bg-[#CCC]'" class="flex w-full max-w-[132px] relative after:content-[''] after:max-w-[132px] after:w-full after:h-1 after:inline-block after:absolute after:top-[12px] after:rounded-s-lg last:after:bg-transparent">
                            <div class="block whitespace-nowrap z-10">
                                <span :class="cart?.complementary_products.length >= i ? 'bg-secondary' : 'bg-[#CCC]'" class="w-[26px] h-[26px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                    <img src="{{ asset('widian-assets/images/tick.svg') }}" class="w-2.5" alt="">
                                </span>
                            </div>
                        </li>
                    </ul>

                    <div class="grid grid-cols-1 xl:grid-cols-2 gap-5 lg:gap-10 xl:gap-20 justify-between">
                        <div v-for="(size, index) in productSizes" :key="size">
                            <div class="flex w-full items-center justify-between mb-2.5">
                                <p class="text-sm leading-[12px] text-dark-gray-2">
                                    @{{ selectedComplementaryProducts[size] ? selectedComplementaryProducts[size].length : 0 }}/@{{ size === '10 ml' ? 1 : (size === '2 ml' ? 6 : 6) }} Selected (@{{ size }})
                                </p>
                            </div>

                            <div class="complementary-product-carousel owl-theme owl-carousel flex mb-[30px]" v-if="complementaryProducts[size] && complementaryProducts[size].length > 0">
                                <div v-for="product in complementaryProducts[size]" :key="product.id" :class="(cart?.complementary_products.length >= 7 || selectedComplementaryProducts[size].length >= (size === '10 ml' ? 1 : (size === '2 ml' ? 6 : 6))) && !selectedComplementaryProducts[size].includes(product.id) ? 'opacity-60 cursor-not-allowed pointer-events-none' : ''" class="item bg-primary flex flex-col items-center p-2">

                                    <div class="w-[132px] h-[132px] mb-2">
                                        <img :src="product.logo_url" class="w-full h-full object-contain" alt="">
                                    </div>

                                    <h4 class="font-playfair text-base leading-[22px] text-dark-gray mb-[6px] tracking-[0.8px]">@{{ product.name }}</h4>

                                    <p class="text-xs leading-[18px] text-dark-gray mb-[5px]">@lang('shop::app.checkout.cart.index.complementary')</p>

                                    <div class="flex items-center gap-2">
                                        <label class="relative flex cursor-pointer items-center rounded-full" :for="`checkbox-${size}-${product.id}`">
                                            <input :id="`checkbox-${size}-${product.id}`" v-model="selectedComplementaryProducts[size]" :value="product.id" type="checkbox" class="before:content[''] peer relative h-3 w-3 cursor-pointer appearance-none rounded-[2px] border border-secondary transition-all before:absolute before:top-2/4 before:left-2/4 before:block checked:border-secondary checked:bg-secondary checked:before:bg-secondary" @change="handleCheckboxChange($event, size, product.id)" />

                                            <div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
                                                <img src="{{ asset('widian-assets/images/tick.svg') }}" class="h-2 w-2" alt="">
                                            </div>
                                        </label>

                                        <label :for="`checkbox-${size}-${product.id}`" class="text-xs leading-[12px] text-secondary mt-[2px] inline-block cursor-pointer">
                                            @lang('shop::app.checkout.cart.mini-cart.select')
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center text-sm text-gray-500 mb-[30px]">
                                @lang('shop::app.checkout.cart.index.no') @{{ size }} @lang('shop::app.checkout.cart.index.complementary-products-available')
                            </div>
                        </div>
                    </div>
                    <div class="px-3 pt-4 flex items-center gap-2 max-w-[1640px] mx-auto">
                        <label class="flex cursor-pointer items-center gap-2">
                            <div class="relative flex cursor-pointer items-center rounded-full">
                                <input type="checkbox" v-model="isAutoSelectEnabled" @change="autoSelect($event)" class="before:content[''] peer relative h-4 w-4 cursor-pointer appearance-none rounded-[2px] border border-secondary transition-all before:absolute before:top-2/4 before:left-2/4 before:block checked:border-secondary checked:bg-secondary checked:before:bg-secondary" />

                                <div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
                                    <img src="{{ asset('widian-assets/images/tick.svg') }}" class="h-2 w-2" alt="">
                                </div>
                            </div>
                            <span class="text-dark-gray font-semibold text-sm leading-none">@lang('shop::app.checkout.cart.index.auto-select')</span>
                        </label>
                    </div>
                </section>

                <!--Cart Summary-->
                <div class="max-w-[1640px] mx-auto mb-20 px-5 sm:px-10">
                    <div class="max-w-[658px] ms-auto">
                        <div class="border-b border-dark-gray-3 text-dark-gray flex justify-between items-center py-[30px] font-bold text-xl leading-[28px]">
                            {!! view_render_event('bagisto.shop.checkout.mini-cart.subtotal.before') !!}

                            <template v-if="!isModifying">
                                <span>@lang('shop::app.checkout.cart.index.subtotal'):</span>

                                <template v-if="displayTax.subtotal == 'including_tax'">
                                    <span>@{{ cart.formatted_sub_total_incl_tax }}</span>
                                </template>

                                <template v-else-if="displayTax.subtotal == 'both'">
                                    <p class="flex flex-col text-3xl font-semibold max-md:text-sm max-sm:text-right">
                                        @{{ cart.formatted_sub_total_incl_tax }}

                                        <span class="text-sm font-normal text-zinc-500 max-sm:text-xs">
                                            @lang('shop::app.checkout.cart.index.excl-tax')

                                            <span>@{{ cart.formatted_sub_total }}</span>
                                        </span>
                                    </p>
                                </template>

                                <template v-else>
                                    <span>@{{ cart.formatted_sub_total }}</span>
                                </template>
                            </template>

                            <template v-else>
                                <!-- Spinner -->
                                <svg
                                    class="text-blue h-8 w-8 animate-spin text-[5px] font-semibold max-md:h-7 max-md:w-7 max-sm:h-4 max-sm:w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    aria-hidden="true"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        class="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        stroke-width="4"
                                    ></circle>

                                    <path
                                        class="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                            </template>

                            {!! view_render_event('bagisto.shop.checkout.mini-cart.subtotal.after') !!}
                        </div>

                        <p class="mb-5 text-xs py-5">
                            @lang('shop::app.checkout.cart.index.promo-code')
                        </p>

                        <button @click="handleProceedToCheckout" class="w-full block text-center capitalize text-base leading-22px bg-secondary hover:bg-black hover:text-white  duration-300 text-white py-3 px-6 rounded-md">
                            @lang('shop::app.checkout.cart.index.proceed-to-checkout')
                        </button>
                    </div>
                </div>
            </template>

            <template v-else>
                <div class="d-flex justify-center align-center py-44">
                    <div class="b-0 grid place-items-center gap-y-5 max-md:gap-y-0">
                        <img class="max-md:h-[100px] max-md:w-[100px]" src="{{ bagisto_asset('images/thank-you.png') }}">

                        <p class="text-xl max-md:text-sm" role="heading">
                            @lang('shop::app.checkout.cart.index.empty-cart')
                        </p>

                        <a href="{{ route('shop.search.index', app()->getLocale()) }}" class="mt-5 inset-x-0 m-auto w-fit px-4 2xl:px-6 py-2 border border-black uppercase text-xs sm:text-sm md:text-md 2xl:text-lg rounded-full hover:bg-secondary hover:text-white hover:border-secondary transition-all duration-500 ease-in-out">@lang('shop::app.checkout.cart.index.continue-shopping')</a>
                    </div>
                </div>
            </template>

            <x-shop::modal ref="howWouldYouLikeToProceedModal">
                <x-slot:header class="!p-4 items-baseline">
                    <h4 class="text-dark-gray font-bold text-start leadinding-[22px]">
                        <span class="text-lg text-dark-gray-2">
                            @lang('shop::app.checkout.cart.index.how-would-you-like-to-proceed')
                        </span>
                    </h4>
                </x-slot:header>
                <x-slot:content>
                    <div class="flex gap-3">
                        <button @click="handleHowToProceed('login')" type="button" class="bg-secondary hover:bg-black duration-300 flex items-center justify-center w-1/3 text-white text-sm uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                            <span class="mt-1">@lang('shop::app.checkout.cart.index.login')</span>
                        </button>
                        <button @click="handleHowToProceed('register')" type="button" class="bg-secondary hover:bg-black duration-300 flex items-center justify-center w-1/3 text-white text-sm uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                            <span class="mt-1">@lang('shop::app.checkout.cart.index.register')</span>
                        </button>
                        <button @click="handleHowToProceed('continue-as-guest')" type="button" class="bg-secondary hover:bg-black duration-300 flex items-center justify-center w-1/3 text-white text-sm uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                            <span class="mt-1">@lang('shop::app.checkout.cart.index.continue-as-guest')</span>
                        </button>
                    </div>
                </x-slot>
            </x-shop::modal>
        </script>

        <script type="module">
            app.component("v-cart", {
                template: '#v-cart-template',

                data() {
                    return  {
                        cart: [],

                        complementaryProducts : {},

                        selectedComplementaryProducts: {},

                        productSizes: [],

                        displayTax: {
                            prices: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_prices') }}",

                            subtotal: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_subtotal') }}",

                            shipping: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_shipping_amount') }}",
                        },

                        isLoading: true,

                        carouselsInitialized: false,

                        isModifying: false,

                        isAutoSelectEnabled: false,

                        isCustomerLoggedIn: "{{ auth()->guard('customer')->check() }}" ?? false,
                    }
                },

                mounted() {
                    this.getCart();
                    this.getComplementaryProduct();

                    this.$emitter.on('update-mini-cart', (cart) => {
                        this.cart = cart;
                        this.getCartComplementaryProducts();
                    });
                },

                watch: {
                    selectedComplementaryProducts: {
                        handler(newVal, oldVal) {
                            this.checkProductLimits();
                        },
                        deep: true
                    }
                },

                methods: {
                    handleProceedToCheckout() {
                        let isComplementaryProductAdded = true;

                        for (const size in this.selectedComplementaryProducts) {
                            if (this.selectedComplementaryProducts[size].length !== this.getSelectionLimit(size)) {
                                isComplementaryProductAdded = false;
                            }
                        }

                        if (!isComplementaryProductAdded) {
                            this.$emitter.emit('add-flash', { type: 'warning', message: '{{ trans('shop::app.checkout.cart.please-select-complementary-products') }}'});
                            return;
                        }

                        if (!this.isCustomerLoggedIn) {
                            this.$refs.howWouldYouLikeToProceedModal.open();
                        } else {
                            window.location.href = '{{ route('shop.checkout.onepage.index', app()->getLocale()) }}';
                        }
                    },

                    handleHowToProceed(type) {
                        this.$refs.howWouldYouLikeToProceedModal.close();
                        if (type === 'login') {
                            window.location.href = '{{ route('shop.customer.session.index', app()->getLocale()) }}';
                        } else if (type === 'register') {
                            window.location.href = '{{ route('shop.customers.register.index', app()->getLocale()) }}';
                        } else if (type === 'continue-as-guest') {
                            window.location.href = '{{ route('shop.checkout.onepage.index', app()->getLocale()) }}';
                        }
                    },

                    getCart() {
                        this.$axios.get('{{ route('shop.api.checkout.cart.index', app()->getLocale()) }}')
                            .then(response => {
                                this.$emitter.emit('update-mini-cart', response.data.data);

                                this.isLoading = false;

                                if (response.data.message) {
                                    this.$emitter.emit('add-flash', { type: 'info', message: response.data.message });
                                }
                            })
                            .catch(error => {});
                    },

                    getComplementaryProduct() {
                        this.$axios.get('{{ route('shop.api.complementary-product.index', app()->getLocale()) }}')
                            .then(response => {
                                const responseData = response.data.data;

                                if (Array.isArray(responseData)) {
                                    this.complementaryProducts = {};
                                    this.productSizes = [];
                                } else {
                                    this.complementaryProducts = responseData;
                                    this.productSizes = Object.keys(responseData);

                                    this.productSizes.forEach(size => {
                                        if (!this.selectedComplementaryProducts[size]) {
                                            this.selectedComplementaryProducts[size] = [];
                                        }
                                    });
                                }

                                this.$nextTick(() => {
                                    this.initAllCarousels();
                                });
                            })
                            .catch(error => {
                                console.error("Failed to load complementary products", error);
                            });
                    },

                    getCartComplementaryProducts() {
                        if (!this.cart) { return; }

                        this.$axios.get('{{ route('shop.api.checkout.cart.complementary-product.index', app()->getLocale()) }}')
                            .then(response => {
                                const selectedProducts = response.data.data;

                                for (const size in selectedProducts) {
                                    this.selectedComplementaryProducts[size] = selectedProducts[size].map(product => product.complementary_product_id);

                                    selectedProducts[size].forEach(product => {
                                        const checkboxId = `checkbox-${size}-${product.complementary_product_id}`;
                                        const checkbox = document.getElementById(checkboxId);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                console.error("Failed to load cart complementary products", error);
                            });
                    },

                    initAllCarousels() {
                        this.destroyAllCarousels();

                        setTimeout(() => {
                            $('.complementary-product-carousel').each(function() {
                                if (!$(this).hasClass('owl-loaded')) {

                                    $(this).owlCarousel({
                                        loop: false,
                                        nav: true,
                                        autoplay: false,
                                        mouseDrag: true,
                                        margin: 10,
                                        dots: false,
                                        startPosition: 0,
                                        navText: [
                                            '<span class="w-[26px] h-[26px] bg-white flex items-center justify-center"><img src="{{ asset("widian-assets/images/arrow-light.svg") }}" class="py-1 px-[6px] cursor-pointer hover:brightness-75 focus:brightness-100" alt=""id="carousel-prev-cart" tabindex="0"></span>',
                                            '<span class="w-[26px] h-[26px] bg-white flex items-center justify-center"><img src="{{ asset("widian-assets/images/arrow-light.svg") }}" class="rotate-180 py-1 px-[6px] cursor-pointer hover:brightness-75 focus:brightness-100" alt="" id="carousel-next-cart" tabindex="0"></span>'
                                        ],
                                        responsive: {
                                            0: {
                                                items: 1,
                                            },
                                            768: {
                                                items: 2,
                                            },
                                            1024: {
                                                items: 4,
                                            },
                                        }
                                    });
                                }
                            });

                            this.carouselsInitialized = true;
                        }, 500);
                    },

                    destroyAllCarousels() {
                        $('.owl-carousel').each(function() {
                            if ($(this).hasClass('owl-loaded')) {
                                $(this).trigger('destroy.owl.carousel')
                                    .removeClass('owl-loaded owl-hidden')
                                    .find('.owl-stage-outer').children().unwrap();
                            }
                        });

                        this.carouselsInitialized = false;
                    },

                    getSelectionLimit(size) {
                        return size === '10 ml' ? 1 : size === '2 ml' ? 6 : 6;
                    },

                    checkProductLimits() {
                        for (const size in this.selectedComplementaryProducts) {
                            const limit = this.getSelectionLimit(size);
                            const selected = this.selectedComplementaryProducts[size];

                            if (selected.length > limit) {
                                const removedProduct = this.selectedComplementaryProducts[size].pop();

                                this.$emitter.emit('add-flash', {
                                    type: 'warning',
                                    message: `You can only select up to ${limit} complementary product${limit > 1 ? 's' : ''} of size ${size}`
                                });

                                const checkboxId = `checkbox-${size}-${removedProduct}`;
                                const checkbox = document.getElementById(checkboxId);
                                if (checkbox) {
                                    checkbox.checked = false;
                                }
                            }
                        }
                    },

                    handleCheckboxChange(event, size, productId) {
                        const isChecked = event.target.checked;

                        this.$axios.post('{{ route("shop.api.checkout.cart.complementary-product.store", app()->getLocale()) }}', {
                            'complementary_product_id': productId,
                            'is_remove': !isChecked
                        })
                        .then(response => {
                            if (response.data.message) {
                                this.getCart();
                                this.$emitter.emit('add-flash', {
                                    type: 'success',
                                    message: response.data.message
                                });
                            }
                        })
                        .catch(error => {
                            this.$emitter.emit('add-flash', {
                                type: 'error',
                                message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.an-error-occurred') }}'
                            });

                            event.target.checked = !isChecked;

                            if (isChecked) {
                                const index = this.selectedComplementaryProducts[size].indexOf(productId);
                                if (index > -1) {
                                    this.selectedComplementaryProducts[size].splice(index, 1);
                                }
                            } else {
                                if (!this.selectedComplementaryProducts[size].includes(productId)) {
                                    this.selectedComplementaryProducts[size].push(productId);
                                }
                            }
                        });
                    },

                    update(quantity, item) {
                        this.isModifying = true;

                        let qty = {};

                        qty[item.id] = quantity;

                        this.$axios.put('{{ route('shop.api.checkout.cart.update', app()->getLocale()) }}', { qty })
                            .then(response => {
                                if (response.data.message) {
                                    this.cart = response.data.data;
                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                    this.$emitter.emit('update-mini-cart', this.cart);
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isModifying = false;
                            }).catch(error => this.isModifying = false);
                    },

                    removeItem(itemId) {
                        this.$emitter.emit('open-confirm-modal', {
                            agree: () => {
                                this.isModifying = true;

                                this.$axios.post('{{ route('shop.api.checkout.cart.destroy', app()->getLocale()) }}', {
                                        '_method': 'DELETE',
                                        'cart_item_id': itemId,
                                    })
                                    .then(response => {
                                        this.cart = response.data.data;

                                        this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                        this.$emitter.emit('update-mini-cart', this.cart);

                                        this.isModifying = false;
                                    }).catch(error => this.isModifying = false);
                            }
                        });
                    },

                    autoSelect(event) {
                        this.isAutoSelectEnabled = event.target.checked;

                        const removeAllSelections = () => {
                            const removePromises = [];

                            for (const size in this.selectedComplementaryProducts) {
                                const selectedIds = [...this.selectedComplementaryProducts[size]];

                                selectedIds.forEach(productId => {
                                    const promise = this.$axios.post('{{ route("shop.api.checkout.cart.complementary-product.store", app()->getLocale()) }}', {
                                        'complementary_product_id': productId,
                                        'is_remove': true
                                    });

                                    removePromises.push(promise);

                                    const checkboxId = `checkbox-${size}-${productId}`;
                                    const checkbox = document.getElementById(checkboxId);
                                    if (checkbox) {
                                        checkbox.checked = false;
                                    }

                                    const index = this.selectedComplementaryProducts[size].indexOf(productId);
                                    if (index > -1) {
                                        this.selectedComplementaryProducts[size].splice(index, 1);
                                    }
                                });
                            }

                            return Promise.all(removePromises);
                        };

                        if (this.isAutoSelectEnabled) {
                            removeAllSelections()
                                .then(() => {
                                    const addPromises = [];

                                    for (const size in this.complementaryProducts) {
                                        if (!this.complementaryProducts[size] || this.complementaryProducts[size].length === 0) {
                                            continue;
                                        }

                                        const limit = this.getSelectionLimit(size);
                                        const availableProducts = this.complementaryProducts[size];

                                        const shuffled = [...availableProducts].sort(() => 0.5 - Math.random());

                                        const selectedCount = Math.min(limit, shuffled.length);
                                        const selectedProducts = shuffled.slice(0, selectedCount);

                                        selectedProducts.forEach(product => {
                                            const promise = this.$axios.post('{{ route("shop.api.checkout.cart.complementary-product.store", app()->getLocale()) }}', {
                                                'complementary_product_id': product.id,
                                                'is_remove': false
                                            });

                                            addPromises.push(promise);

                                            const checkboxId = `checkbox-${size}-${product.id}`;
                                            const checkbox = document.getElementById(checkboxId);
                                            if (checkbox) {
                                                checkbox.checked = true;
                                            }

                                            if (!this.selectedComplementaryProducts[size].includes(product.id)) {
                                                this.selectedComplementaryProducts[size].push(product.id);
                                            }
                                        });
                                    }

                                    Promise.all(addPromises)
                                        .then(() => {
                                            this.getCart();
                                            this.$emitter.emit('add-flash', {
                                                type: 'success',
                                                message: '{{ trans('shop::app.checkout.cart.complementary-products-auto-selected') }}'
                                            });
                                        })
                                        .catch(error => {
                                            this.$emitter.emit('add-flash', {
                                                type: 'error',
                                                message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.failed-to-auto-select') }}'
                                            });
                                        });
                                })
                                .catch(error => {
                                    this.$emitter.emit('add-flash', {
                                        type: 'error',
                                        message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.failed-to-clear-previous-selections') }}'
                                    });
                                });
                        } else {
                            removeAllSelections()
                                .then(() => {
                                    this.getCart();
                                    this.$emitter.emit('add-flash', {
                                        type: 'success',
                                        message: '{{ trans('shop::app.checkout.cart.all-complementary-products-removed') }}'
                                    });
                                })
                                .catch(error => {
                                    this.$emitter.emit('add-flash', {
                                        type: 'error',
                                        message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.failed-to-remove-complementary-products') }}'
                                    });
                                });
                        }
                    },
                }
            });
        </script>
    @endpushOnce
</x-shop::layouts>
