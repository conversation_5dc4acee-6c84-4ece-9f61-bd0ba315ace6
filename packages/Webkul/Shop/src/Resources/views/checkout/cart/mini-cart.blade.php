<!-- Mini Cart Vue Component -->
<v-mini-cart>
    <span
        class="icon-cart cursor-pointer text-2xl"
        role="button"
        aria-label="@lang('shop::app.checkout.cart.mini-cart.shopping-cart')"
    ></span>
</v-mini-cart>

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-mini-cart-template"
    >
        {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.before') !!}

        @if (core()->getConfigData('sales.checkout.mini_cart.display_mini_cart'))
            <x-shop::drawer ref="mini-cart-drawer">
                <!-- Drawer Toggler -->
                <x-slot:toggle>
                    {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.toggle.before') !!}

                    <span class="relative">
                        <span
                            class="cursor-pointer text-2xl"
                            role="button"
                            aria-label="@lang('shop::app.checkout.cart.mini-cart.shopping-cart')"
                            tabindex="0"
                        >
                            <img src="{{ asset('widian-assets/images/cart-bag.svg') }}" class="h-5 min-w-5 max-w-5" alt="icon">
                        </span>

                        @if (core()->getConfigData('sales.checkout.my_cart.summary') == 'display_item_quantity')
                            <span
                                class="absolute -top-4 rounded-[44px] bg-secondary px-2 py-1.5 text-xs font-semibold leading-[9px] text-white ltr:left-5 rtl:right-5 max-md:ltr:left-4 max-md:rtl:right-4"
                                v-if="cart?.items_qty"
                            >
                                @{{ cart.items_qty }}
                            </span>
                        @else
                            <span
                                class="absolute -top-4 rounded-[44px] bg-secondary px-2 py-1.5 text-xs font-semibold leading-[9px] text-white ltr:left-5 rtl:right-5 max-md:px-2 max-md:py-1.5 max-md:ltr:left-4 max-md:rtl:right-4"
                                v-if="cart?.items_count"
                            >
                                @{{ cart.items_count }}
                            </span>
                        @endif
                    </span>

                    {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.toggle.after') !!}
                </x-slot>

                <!-- Drawer Header -->
                <x-slot:header>
                    {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.header.before') !!}

                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <img class="h-6 w-6" src="{{ asset('widian-assets/images/cart-bag.svg') }}" alt="Cart">
                            <h2 class="text-lg font-semibold leading-none">@lang('shop::app.checkout.cart.mini-cart.bag') <span v-if="cart?.items_qty">(@{{ cart.items_qty }})</span></h2>
                        </div>
                    </div>

                    {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.header.after') !!}
                </x-slot>

                <!-- Drawer Content -->
                <x-slot:content>
                    {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.before') !!}

                    <!-- Cart Item Listing -->
                    <div v-if="cart?.items?.length" v-for="item in cart?.items" class="flex flex-wrap sm:flex-nowrap gap-5 border-b border-light-gray pb-5 mb-5">
                        <!-- Cart Item Image -->
                        {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.image.before') !!}

                        <a class="w-full sm:w-auto" :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', item.product_url_key)">
                          <img :src="item.base_image.original_image_url" :alt="item.name" class="w-[190px] h-[190px] object-cover aspect-square mx-auto sm:mx-0">
                        </a>

                        {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.image.after') !!}

                        <!-- Middle Content -->
                        <div class="flex flex-col justify-between grow sm:min-w-[200px]">
                            <div>
                                {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.name.before') !!}

                                <p class="font-regular text-dark-gray leading-[22px] mb-2">@{{ item.categories && item.categories.length > 0 ? item.categories[0].name : '' }}</p>
                                <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', item.product_url_key)">
                                    <p class="font-bold font-playfair text-[22px] leading-[28px]">@{{ item.name }}</p>
                                </a>

                                {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.name.after') !!}
                            </div>

                            <div v-if="item.has_gift" class="flex gap-2 items-center text-black text-md leading-normal relative">
                                <img src="{{ asset('widian-assets/images/gift.svg') }}" alt="Gift Icon" />
                                <span class="mt-1">@lang('shop::app.products.view.gift.added-as-a-gift')</span>
                            </div>

                            <!-- Cart Item Quantity Changer -->
                            {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.quantity_changer.before') !!}

                            <x-shop::quantity-changer
                                class="text-dark-gray flex justify-start border border-[#CCC] w-fit mt-4 sm:mt-0"
                                name="quantity"
                                ::max="item?.stock_qty"
                                ::value="item?.quantity"
                                @change="updateItem($event, item)"
                            />

                            {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.quantity_changer.after') !!}
                        </div>

                        <div class="flex flex-col justify-between items-end text-right grow sm:grow-0 sm:w-auto min-w-[120px]">
                            {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.remove_button.before') !!}

                            <button type="button" class="font-bold text-xs leading-[18px] text-dark-gray" @click="removeItem(item.id)">
                                @lang('shop::app.checkout.cart.mini-cart.remove')
                            </button>

                            <span v-if="item.has_gift" class="mt-5 flex gap-2 items-center text-black text-md ">@lang('shop::app.products.view.gift.free')</span>

                            {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.remove_button.after') !!}

                            <p class="font-bold text-lg leading-[22px] text-dark-gray flex items-center justify-end gap-2 mt-4 sm:mt-0">
                                {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.price.before') !!}

                                <template v-if="displayTax.prices == 'including_tax'">
                                    <span>
                                        @{{ item.formatted_total_incl_tax }}
                                    </span>
                                </template>

                                <template v-else-if="displayTax.prices == 'both'">
                                    <span>
                                        @{{ item.formatted_total_incl_tax }}

                                        <span class="text-xs font-normal text-zinc-500">
                                            @lang('shop::app.checkout.cart.mini-cart.excl-tax')

                                            <span class="font-medium text-black">@{{ item.formatted_total }}</span>
                                        </span>
                                    </span>
                                </template>

                                <template v-else>
                                    <span>
                                        @{{ item.formatted_total }}
                                    </span>
                                </template>

                                {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.price.after') !!}
                            </p>
                        </div>
                    </div>

                    <!-- Empty Cart Section -->
                    <div v-else class="mt-32 pb-8 max-md:mt-32">
                        <div class="b-0 grid place-items-center gap-y-5 max-md:gap-y-0">
                            <img class="max-md:h-[100px] max-md:w-[100px]" src="{{ bagisto_asset('images/thank-you.png') }}">
                            <p class="text-xl max-md:text-sm" role="heading">@lang('shop::app.checkout.cart.mini-cart.empty-cart')</p>
                            <a href="{{ route('shop.search.index', app()->getLocale()) }}" class="mt-5 inset-x-0 m-auto w-fit px-4 2xl:px-6 py-2 border border-black uppercase text-xs sm:text-sm md:text-md 2xl:text-lg rounded-full hover:bg-secondary hover:text-white hover:border-secondary transition-all duration-500 ease-in-out">@lang('shop::app.checkout.cart.index.continue-shopping')</a>
                        </div>
                    </div>

                    <!-- complimentary samples -->
                    <section v-if="cart?.items?.length && productSizes.length > 0">
                        <h4 class="text-dark-gray font-bold text-base leadinding-[22px] mb-[30px]">@lang('shop::app.checkout.cart.index.complimentary-samples')</h4>

                        <ul class="flex items-center w-full mb-[30px]">
                            <li v-for="i in 7" :key="i" :class="(cart?.complementary_products.length - 1) >= i ? 'after:bg-secondary' : 'after:bg-[#CCC]'" class="flex w-full max-w-[132px] relative last:after:bg-transparent after:content-[''] after:max-w-[132px] after:w-full after:h-1 after:inline-block after:absolute after:top-2">
                                <div class="block whitespace-nowrap z-10">
                                    <span :class="cart?.complementary_products.length >= i ? 'bg-secondary' : 'bg-[#CCC]'" class="w-[18px] h-[18px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                        <img src="{{ asset('widian-assets/images/tick.svg') }}" class="w-2.5" alt="">
                                    </span>
                                </div>
                            </li>
                        </ul>

                        <div>
                            <div class="py-2 flex items-center gap-2 mb-3">
                                <label class="flex cursor-pointer items-center gap-2">
                                    <div class="relative flex cursor-pointer items-center rounded-full">
                                        <input type="checkbox" v-model="isAutoSelectEnabled" @change="autoSelect($event)" class="before:content[''] peer relative h-4 w-4 cursor-pointer appearance-none rounded-[2px] border border-secondary transition-all before:absolute before:top-2/4 before:left-2/4 before:block checked:border-secondary checked:bg-secondary checked:before:bg-secondary" />

                                        <div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
                                            <img src="{{ asset('widian-assets/images/tick.svg') }}" class="h-2 w-2" alt="">
                                        </div>
                                    </div>
                                    <span class="text-dark-gray font-semibold text-sm leading-none">@lang('shop::app.checkout.cart.mini-cart.auto-select')</span>
                                </label>
                            </div>
                            <div v-for="(size, index) in productSizes" :key="size" class="mb-[30px]">
                                <div class="flex w-full items-center justify-between mb-2.5">
                                    <p class="text-sm leading-[12px] text-dark-gray-2">
                                        @{{ selectedComplementaryProducts[size] ? selectedComplementaryProducts[size].length : 0 }}/@{{ size === '10 ml' ? 1 : (size === '2 ml' ? 6 : 6) }} Selected (@{{ size }})
                                    </p>
                                </div>

                                <div class="complementary-product-carousel owl-theme owl-carousel flex mb-[30px]" v-if="complementaryProducts[size] && complementaryProducts[size].length > 0">
                                    <div v-for="product in complementaryProducts[size]" :key="product.id" :class="(cart?.complementary_products.length >= 7 || selectedComplementaryProducts[size].length >= (size === '10 ml' ? 1 : (size === '2 ml' ? 6 : 6))) && !selectedComplementaryProducts[size].includes(product.id) ? 'opacity-60 cursor-not-allowed pointer-events-none' : ''" class="item bg-primary flex flex-col items-center p-2">
                                        <img :src="product.logo_url" class="w-[132px] h-[132px] object-contain" alt="">

                                        <h4 class="font-playfair text-base leading-[22px] text-dark-gray mb-[6px] tracking-[0.8px]">@{{ product.name }}</h4>

                                        <p class="text-xs leading-[18px] text-dark-gray mb-[5px]">@lang('shop::app.checkout.cart.mini-cart.complementary')</p>

                                        <div class="flex items-center gap-2">
                                            <label class="relative flex cursor-pointer items-center rounded-full" :for="`checkbox-${size}-${product.id}`">
                                                <input :id="`checkbox-${size}-${product.id}`" v-model="selectedComplementaryProducts[size]" :value="product.id" type="checkbox" class="before:content[''] peer relative h-3 w-3 cursor-pointer appearance-none rounded-[2px] border border-secondary transition-all before:absolute before:top-2/4 before:left-2/4 before:block checked:border-secondary checked:bg-secondary checked:before:bg-secondary" @change="handleCheckboxChange($event, size, product.id)" />

                                                <div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
                                                    <img src="{{ asset('widian-assets/images/tick.svg') }}" class="h-2 w-2" alt="">
                                                </div>
                                            </label>

                                            <label :for="`checkbox-${size}-${product.id}`" class="text-xs leading-[12px] text-secondary mt-[2px] inline-block cursor-pointer">
                                                @lang('shop::app.checkout.cart.mini-cart.select')
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div v-else class="text-center text-sm text-gray-500 mb-[30px]">
                                    @lang('shop::app.checkout.cart.mini-cart.no') @{{ size }} @lang('shop::app.checkout.cart.mini-cart.complementary-products-available')
                                </div>
                            </div>
                        </div>
                    </section>

                    {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.content.after') !!}
                </x-slot>

            <!-- Drawer Footer -->
            <x-slot:footer>
                <div v-if="cart?.items?.length">
                    <div class="text-dark-gray flex justify-between items-center font-bold text-lg mb-5 leading-[22px]">
                        {!! view_render_event('bagisto.shop.checkout.mini-cart.subtotal.before') !!}

                        <template v-if="!isLoading">
                            <span>@lang('shop::app.checkout.cart.mini-cart.subtotal'):</span>

                            <template v-if="displayTax.subtotal == 'including_tax'">
                                <span>@{{ cart.formatted_sub_total_incl_tax }}</span>
                            </template>

                            <template v-else-if="displayTax.subtotal == 'both'">
                                <p class="flex flex-col text-3xl font-semibold max-md:text-sm max-sm:text-right">
                                    @{{ cart.formatted_sub_total_incl_tax }}

                                    <span class="text-sm font-normal text-zinc-500 max-sm:text-xs">
                                        @lang('shop::app.checkout.cart.mini-cart.excl-tax')

                                        <span>@{{ cart.formatted_sub_total }}</span>
                                    </span>
                                </p>
                            </template>

                            <template v-else>
                                <span>@{{ cart.formatted_sub_total }}</span>
                            </template>
                        </template>

                        <template v-else>
                            <!-- Spinner -->
                            <svg
                                class="text-blue h-8 w-8 animate-spin text-[5px] font-semibold max-md:h-7 max-md:w-7 max-sm:h-4 max-sm:w-4"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                aria-hidden="true"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    class="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    stroke-width="4"
                                ></circle>

                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                        </template>

                        {!! view_render_event('bagisto.shop.checkout.mini-cart.subtotal.after') !!}
                    </div>

                    <p class="mb-5 text-xs">
                        @lang('shop::app.checkout.cart.mini-cart.promo-code-text')
                    </p>

                    {!! view_render_event('bagisto.shop.checkout.mini-cart.action.before') !!}

                    <div class="flex max-sm:flex-col gap-4">
                        <a class="flex-1 text-center text-base leading-22px bg-secondary hover:bg-black duration-300 text-white py-3 px-6 rounded-md"
                            href="{{ route('shop.checkout.cart.index', app()->getLocale()) }}">
                            @lang('shop::app.checkout.cart.mini-cart.go-to-cart')
                        </a>

                        {!! view_render_event('bagisto.shop.checkout.mini-cart.continue_to_checkout.before') !!}

                        <a href="{{ route('shop.checkout.onepage.index', app()->getLocale()) }}"
                            class="flex-1 text-center text-base leading-22px hover:bg-secondary hover:border hover:border-transparent hover:text-white border border-dark-gray py-3 px-6 rounded-md text-dark-gray duration-300">
                            @lang('shop::app.checkout.cart.mini-cart.proceed-to-checkout')
                        </a>

                        {!! view_render_event('bagisto.shop.checkout.mini-cart.continue_to_checkout.after') !!}
                    </div>

                    {!! view_render_event('bagisto.shop.checkout.mini-cart.action.after') !!}
                </div>

                </x-slot>
            </x-shop::drawer>

        @else
            <a href="{{ route('shop.checkout.onepage.index', app()->getLocale()) }}">
                {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.toggle.before') !!}

                    <span class="relative">
                        <span
                            class="icon-cart cursor-pointer text-2xl"
                            role="button"
                            aria-label="@lang('shop::app.checkout.cart.mini-cart.shopping-cart')"
                            tabindex="0"
                        ></span>

                        <span
                            class="absolute -top-4 rounded-[44px] bg-navyBlue px-2 py-1.5 text-xs font-semibold leading-[9px] text-white ltr:left-5 rtl:right-5 max-md:px-2 max-md:py-1.5 max-md:ltr:left-4 max-md:rtl:right-4"
                            v-if="cart?.items_qty"
                        >
                            @{{ cart.items_qty }}
                        </span>
                    </span>

                {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.toggle.after') !!}
            </a>
        @endif

        {!! view_render_event('bagisto.shop.checkout.mini-cart.drawer.after') !!}
    </script>

    <script type="module">
        app.component("v-mini-cart", {
            template: '#v-mini-cart-template',

            data() {
                return {
                    cart: null,
                    complementaryProducts: {},
                    selectedComplementaryProducts: {},
                    productSizes: [],
                    isLoading: false,
                    carouselsInitialized: false,
                    isAutoSelectEnabled: false,
                    displayTax: {
                        prices: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_prices') }}",
                        subtotal: "{{ core()->getConfigData('sales.taxes.shopping_cart.display_subtotal') }}",
                    }
                }
            },

            mounted() {
                this.getCart();
                this.getComplementaryProduct();

                this.$emitter.on('update-mini-cart', (cart) => {
                    this.cart = cart;
                    this.getCartComplementaryProducts();
                });
            },

            watch: {
                selectedComplementaryProducts: {
                    handler(newVal, oldVal) {
                        this.checkProductLimits();
                    },
                    deep: true
                }
            },

            methods: {
                getCart() {
                    this.$axios.get('{{ route('shop.api.checkout.cart.index', app()->getLocale()) }}')
                        .then(response => {
                            this.$emitter.emit('update-mini-cart', response.data.data);
                        })
                        .catch(error => {
                            console.error("Failed to load cart", error);
                        });
                },

                getComplementaryProduct() {
                    this.$axios.get('{{ route('shop.api.complementary-product.index', app()->getLocale()) }}')
                        .then(response => {
                            const responseData = response.data.data;

                            if (Array.isArray(responseData)) {
                                this.complementaryProducts = {};
                                this.productSizes = [];
                            } else {
                                this.complementaryProducts = responseData;
                                this.productSizes = Object.keys(responseData);

                                this.productSizes.forEach(size => {
                                    if (!this.selectedComplementaryProducts[size]) {
                                        this.selectedComplementaryProducts[size] = [];
                                    }
                                });
                            }

                            this.$nextTick(() => {
                                this.initAllCarousels();
                            });
                        })
                        .catch(error => {
                            console.error("Failed to load complementary products", error);
                        });
                },

                getCartComplementaryProducts() {
                    if (!this.cart) { return; }

                    this.$axios.get('{{ route('shop.api.checkout.cart.complementary-product.index', app()->getLocale()) }}')
                        .then(response => {
                            const selectedProducts = response.data.data;

                            for (const size in selectedProducts) {
                                this.selectedComplementaryProducts[size] = selectedProducts[size].map(product => product.complementary_product_id);

                                selectedProducts[size].forEach(product => {
                                    const checkboxId = `checkbox-${size}-${product.complementary_product_id}`;
                                    const checkbox = document.getElementById(checkboxId);
                                    if (checkbox) {
                                        checkbox.checked = true;
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            console.error("Failed to load cart complementary products", error);
                        });
                },

                initAllCarousels() {
                    this.destroyAllCarousels();

                    $('.complementary-product-carousel').each(function() {
                        if (!$(this).hasClass('owl-loaded')) {

                            $(this).owlCarousel({
                                loop: false,
                                nav: true,
                                autoplay: false,
                                mouseDrag: true,
                                dots: false,
                                margin: 10,
                                startPosition: 0,
                                navText: [
                                    '<svg class="rotate-180" width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" /></svg>',
                                    '<svg width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" /></svg>'
                                ],
                                responsive: {
                                    0: {
                                        items: 1,
                                    },
                                    768: {
                                        items: 2,
                                    },
                                    1024: {
                                        items: 4,
                                    },
                                }
                            });
                        }
                    });

                    this.carouselsInitialized = true;
                },

                destroyAllCarousels() {
                    $('.owl-carousel').each(function() {
                        if ($(this).hasClass('owl-loaded')) {
                            $(this).trigger('destroy.owl.carousel')
                                .removeClass('owl-loaded owl-hidden')
                                .find('.owl-stage-outer').children().unwrap();
                        }
                    });

                    this.carouselsInitialized = false;
                },

                updateItem(quantity, item) {
                    this.isLoading = true;

                    let qty = {};
                    qty[item.id] = quantity;

                    this.$axios.put('{{ route('shop.api.checkout.cart.update', app()->getLocale()) }}', { qty })
                        .then(response => {
                            if (response.data.message) {
                                this.cart = response.data.data;
                                this.$emitter.emit('update-mini-cart', this.cart);
                            } else {
                                this.$emitter.emit('add-flash', {
                                    type: 'warning',
                                    message: response.data.data.message
                                });
                            }

                            this.isLoading = false;
                        })
                        .catch(() => {
                            this.isLoading = false;
                        });
                },

                removeItem(itemId) {
                    this.$refs['mini-cart-drawer'].close();
                    this.$emitter.emit('open-confirm-modal', {
                        agree: () => {
                            this.isLoading = true;

                            this.$axios.post('{{ route('shop.api.checkout.cart.destroy', app()->getLocale()) }}', {
                                '_method': 'DELETE',
                                'cart_item_id': itemId,
                            })
                                .then(response => {
                                    this.cart = response.data.data;

                                    this.$emitter.emit('add-flash', {
                                        type: 'success',
                                        message: response.data.message
                                    });

                                    this.$emitter.emit('update-mini-cart', this.cart);

                                    this.$refs['mini-cart-drawer'].open();

                                    this.isLoading = false;
                                })
                                .catch(error => {
                                    this.$emitter.emit('add-flash', {
                                        type: 'error',
                                        message: response.data.message
                                    });

                                    this.isLoading = false;
                                });
                        },
                        disagree: () => {
                            this.$refs['mini-cart-drawer'].open();
                        }
                    });
                },

                checkProductLimits() {
                    for (const size in this.selectedComplementaryProducts) {
                        const limit = this.getSelectionLimit(size);
                        const selected = this.selectedComplementaryProducts[size];

                        if (selected.length > limit) {
                            const removedProduct = selected.pop();

                            this.$emitter.emit('add-flash', {
                                type: 'warning',
                                message: `You can only select up to ${limit} complementary product${limit > 1 ? 's' : ''} of size ${size}`
                            });

                            const checkboxId = `checkbox-${size}-${removedProduct}`;
                            const checkbox = document.getElementById(checkboxId);
                            if (checkbox) {
                                checkbox.checked = false;
                            }
                        }
                    }
                },

                getSelectionLimit(size) {
                    return size === '10 ml' ? 1 : size === '2 ml' ? 6 : 6;
                },

                handleCheckboxChange(event, size, productId) {
                    const isChecked = event.target.checked;

                    this.$axios.post('{{ route("shop.api.checkout.cart.complementary-product.store", app()->getLocale()) }}', {
                        'complementary_product_id': productId,
                        'is_remove': !isChecked
                    })
                    .then(response => {
                        if (response.data.message) {
                            this.getCart();

                            this.$emitter.emit('complementary-product-updated');
                        }
                    })
                    .catch(error => {
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.an-error-occurred') }}'
                        });

                        event.target.checked = !isChecked;

                        if (isChecked) {
                            const index = this.selectedComplementaryProducts[size].indexOf(productId);
                            if (index > -1) {
                                this.selectedComplementaryProducts[size].splice(index, 1);
                            }
                        } else {
                            if (!this.selectedComplementaryProducts[size].includes(productId)) {
                                this.selectedComplementaryProducts[size].push(productId);
                            }
                        }
                    });
                },

                autoSelect(event) {
                    this.isAutoSelectEnabled = event.target.checked;

                    const removeAllSelections = () => {
                        const removePromises = [];

                        for (const size in this.selectedComplementaryProducts) {
                            const selectedIds = [...this.selectedComplementaryProducts[size]];

                            selectedIds.forEach(productId => {
                                const promise = this.$axios.post('{{ route("shop.api.checkout.cart.complementary-product.store", app()->getLocale()) }}', {
                                    'complementary_product_id': productId,
                                    'is_remove': true
                                });

                                removePromises.push(promise);

                                const checkboxId = `checkbox-${size}-${productId}`;
                                const checkbox = document.getElementById(checkboxId);
                                if (checkbox) {
                                    checkbox.checked = false;
                                }

                                const index = this.selectedComplementaryProducts[size].indexOf(productId);
                                if (index > -1) {
                                    this.selectedComplementaryProducts[size].splice(index, 1);
                                }
                            });
                        }

                        return Promise.all(removePromises);
                    };

                    if (this.isAutoSelectEnabled) {
                        removeAllSelections()
                            .then(() => {
                                const addPromises = [];

                                for (const size in this.complementaryProducts) {
                                    if (!this.complementaryProducts[size] || this.complementaryProducts[size].length === 0) {
                                        continue;
                                    }

                                    const limit = this.getSelectionLimit(size);
                                    const availableProducts = this.complementaryProducts[size];

                                    const shuffled = [...availableProducts].sort(() => 0.5 - Math.random());

                                    const selectedCount = Math.min(limit, shuffled.length);
                                    const selectedProducts = shuffled.slice(0, selectedCount);

                                    selectedProducts.forEach(product => {
                                        const promise = this.$axios.post('{{ route("shop.api.checkout.cart.complementary-product.store", app()->getLocale()) }}', {
                                            'complementary_product_id': product.id,
                                            'is_remove': false
                                        });

                                        addPromises.push(promise);

                                        const checkboxId = `checkbox-${size}-${product.id}`;
                                        const checkbox = document.getElementById(checkboxId);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }

                                        if (!this.selectedComplementaryProducts[size].includes(product.id)) {
                                            this.selectedComplementaryProducts[size].push(product.id);
                                        }
                                    });
                                }

                                Promise.all(addPromises)
                                    .then(() => {
                                        this.getCart();
                                        this.$emitter.emit('add-flash', {
                                            type: 'success',
                                            message: '{{ trans('shop::app.checkout.cart.complementary-products-auto-selected') }}'
                                        });
                                        this.$emitter.emit('complementary-product-updated');
                                    })
                                    .catch(error => {
                                        this.$emitter.emit('add-flash', {
                                            type: 'error',
                                            message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.failed-to-auto-select') }}'
                                        });
                                    });
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.failed-to-clear-previous-selections') }}'
                                });
                            });
                    } else {
                        removeAllSelections()
                            .then(() => {
                                this.getCart();
                                this.$emitter.emit('add-flash', {
                                    type: 'success',
                                    message: '{{ trans('shop::app.checkout.cart.all-complementary-products-removed') }}'
                                });
                                this.$emitter.emit('complementary-product-updated');
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: error.response?.data?.message || '{{ trans('shop::app.checkout.cart.failed-to-remove-complementary-products') }}'
                                });
                            });
                    }
                },
            }
        });
    </script>

@endpushOnce