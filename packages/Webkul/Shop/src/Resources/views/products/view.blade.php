@inject('reviewHelper', 'Webkul\Product\Helpers\Review')
@inject('productViewHelper', 'Webkul\Product\Helpers\View')
@inject('attibuteOption', 'Webkul\Attribute\Models\AttributeOption')
@inject('themeCustomizationRepository', 'Webkul\Theme\Repositories\ThemeCustomizationRepository')
@inject('categoryRepository', 'Webkul\Category\Repositories\CategoryRepository')

@php
    $channel = core()->getCurrentChannel();

    $avgRatings = $reviewHelper->getAverageRating($product);

    $percentageRatings = $reviewHelper->getPercentageRating($product);

    $customAttributeValues = $productViewHelper->getAdditionalData($product);

    $attributeData = collect($customAttributeValues)->filter(fn ($item) => ! empty($item['value']));

    $productSize = $attibuteOption->find($product->size) ?? [];

    $notesIngredients = $themeCustomizationRepository->findOneWhere([
        'type' => 'static_content',
        'name' => 'Notes & Ingredients',
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);
@endphp

<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="{{ trim($product->meta_description) != "" ? $product->meta_description : \Illuminate\Support\Str::limit(strip_tags($product->description), 120, '') }}"/>

    <meta name="keywords" content="{{ $product->meta_keywords }}"/>

    @if (core()->getConfigData('catalog.rich_snippets.products.enable'))
        <script type="application/ld+json">
            {!! app('Webkul\Product\Helpers\SEO')->getProductJsonLd($product) !!}
        </script>
    @endif

    <meta name="twitter:card" content="summary_large_image" />

    <meta name="twitter:title" content="{{ $product->name }}" />

    <meta name="twitter:description" content="{!! htmlspecialchars(trim(strip_tags($product->description))) !!}" />

    <meta name="twitter:image:alt" content="" />

    <meta name="twitter:image" content="{{ product_image()->getProductBaseImage($product)['original_image_url'] }}" />

    <meta property="og:type" content="og:product" />

    <meta property="og:title" content="{{ $product->name }}" />

    <meta property="og:image" content="{{ product_image()->getProductBaseImage($product)['original_image_url'] }}" />

    <meta property="og:description" content="{!! htmlspecialchars(trim(strip_tags($product->description))) !!}" />

    <meta property="og:url" content="{{ route('shop.product.index', [app()->getLocale(), $product->url_key]) }}" />
@endPush

<!-- Page Layout -->
<x-shop::layouts>
    <!-- Page Title -->
    <x-slot:title>
        {{ trim($product->meta_title) != "" ? $product->meta_title : $product->name }}
    </x-slot>

    <v-product>
        <x-shop::shimmer.products.view />
    </v-product>

    <you-may-also-like-products />

    @pushOnce('styles')
        <style>
            .accordion-enter-active,
            .accordion-leave-active {
                transition: max-height 0.3s ease, opacity 0.3s ease;
                max-height: 300px;
                overflow: hidden;
            }

            .accordion-enter-from,
            .accordion-leave-to {
                max-height: 0;
                opacity: 0;
            }
        </style>
    @endPushOnce

    @pushOnce('scripts')
        <script type="text/x-template" id="v-product-template">
            <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
                <form ref="formData" @submit="handleSubmit($event, addToCart)">
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    <input type="hidden" name="is_buy_now" v-model="is_buy_now">

                    <section class="grid grid-cols-1 lg:grid-cols-2">
                        <div class="flex flex-col">
                            <div class="flex flex-col">
                                <img class="w-full max-h-[600px] object-cover" src="{{ product_image()->getProductBaseImage($product)['original_image_url'] }}" alt="{{ $product->name }}" />
                            </div>

                            @if (isset($product->other_images) && !empty($product->other_images))
                                <div v-if="!isCarouselLoaded">
                                    <x-shop::shimmer.products.images/>
                                </div>
                                <div class="product-image-carousel owl-theme owl-carousel">
                                    @foreach($product->other_images as $key => $image)
                                        <div class="item">
                                            <img src="{{ asset($image->url) }}" class="w-full h-full object-cover" alt="{{ $image->title ?? $product->name }}">
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>

                        <div class="pt-10 px-5 lg:px-8 xl:px-14 2xl:px-20 sticky h-fit top-[252px]">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4">
                                <h1 class="text-secondary text-4xl xl:text-5xl font-medium uppercase font-playfair leading-[60px] mb-3">{{ $product->name }}</h1>
                                @if (isset($product->categories) && !empty($product->categories) && $product->categories->count() > 0 && $product->categories->where('category_type', 0)->first())
                                    <span class="text-black text-lg xl:text-xl font-normal">{{ $product->categories->where('category_type', 0)->first()->name }}</span>
                                @endif
                            </div>

                            @if (isset($product->product_tagline) && !empty($product->product_tagline))
                                <p class="text-black text-lg xl:text-xl font-normal font-playfair leading-7 mb-3 mt-3 sm:mt-0">
                                    {{ $product->product_tagline }}
                                </p>
                            @endif

                            @if (isset($product->product_emotion) && !empty($product->product_emotion))
                                <span class="mb-10 flex">
                                    <strong>@lang('shop::app.products.view.emotion'): &nbsp;</strong>
                                    {{ $product->product_emotion }}
                                </span>
                            @endif

                            <div class="flex items-center gap-6 pb-6 border-b border-light-gray">
                                @if(isset($product->special_price) && !empty($product->special_price))
                                    <span class="text-black text-3xl xl:text-4xl font-normal capitalize leading-10">{{ core()->formatPrice(core()->convertPrice($product->special_price, core()->getCurrentCurrencyCode(), core()->getCurrentCurrencyCode())) }}</span>
                                    <span class="opacity-50 text-black text-xl xl:text-2xl font-normal line-through capitalize leading-loose">{{ core()->formatPrice(core()->convertPrice($product->price, core()->getCurrentCurrencyCode(), core()->getCurrentCurrencyCode())) }}</span>
                                @else
                                    <span class="text-black text-3xl xl:text-4xl font-normal capitalize leading-10">{{ core()->formatPrice(core()->convertPrice($product->price, core()->getCurrentCurrencyCode(), core()->getCurrentCurrencyCode())) }}</span>
                                @endif
                            </div>

                            <div class="space-y-10">
                                @if(isset($productSize) && !empty($productSize) && isset($productSize->admin_name) && !empty($productSize->admin_name))
                                    <div class="mt-2.5">
                                        <h6 class="text-black text-base font-normal leading-snug mb-2">@lang('shop::app.products.view.size')</h6>
                                        <div class="flex flex-wrap gap-5">
                                            <div class="text-black text-base font-normal leading-snug p-3 border border-black inline-flex">
                                                {{ $productSize->admin_name }} @lang('shop::app.products.view.extrait-de-parfum')
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <x-shop::quantity-changer ::max="{{ $product->inventories->sum('qty') }}" name="quantity" ::value="1" class="mt-2.5 inline-flex border border-light-gray"/>

                                <x-shop::drawer ref="giftDrawer" position="right" width="600px">
                                    <x-slot:toggle>
                                        <div onclick="event.stopPropagation();">
                                            {!! view_render_event('bagisto.shop.products.view.make-it-a-gift.drawer.toggle.before') !!}

                                            <div class="flex items-center justify-between w-full">
                                                <div class="flex items-center gap-[10px]">
                                                    <button @click="openGiftDrawer" type="button">
                                                        <div v-if="!isGift" class="flex gap-2 items-center text-black text-lg font-bold uppercase leading-normal relative">
                                                            <img src="{{ asset('widian-assets/images/gift.svg') }}" alt="Gift Icon" />
                                                            <span class="mt-1">@lang('shop::app.products.view.gift.make-it-a-gift')</span>
                                                        </div>
                                                        <div v-else class="flex gap-2 items-center text-black text-lg font-bold uppercase leading-normal relative">
                                                            <img src="{{ asset('widian-assets/images/gift-added.svg') }}" alt="Gift Added Icon" />
                                                            <span class="mt-1">@lang('shop::app.products.view.gift.modify-gift')</span>
                                                        </div>
                                                    </button>
                                                    <span v-if="isGift" @click="removeGiftDetails()" class="icon-cancel cursor-pointer text-2xl max-md:text-xl" title="@lang('shop::app.products.view.gift.remove-gift')"></span>
                                                </div>

                                                <div class="flex items-center gap-[10px]">
                                                    <button type="button" @click="addToWishlist()" class="p-2 bg-black hover:bg-secondary rounded transition">
                                                        <img v-if="!isWishlist" src="{{ asset('widian-assets/images/heart.svg') }}" alt="Wishlist Icon" title="@lang('shop::app.products.view.add-to-wishlist')" class="w-5 h-5" />
                                                        <img v-else src="{{ asset('widian-assets/images/heart-fill.svg') }}" alt="Wishlist Icon" title="@lang('shop::app.products.view.wishlisted')" class="w-5 h-5" />
                                                    </button>
                                                    {!! view_render_event('bagisto.shop.products.view.compare.after', ['product' => $product]) !!}
                                                </div>
                                            </div>

                                            {!! view_render_event('bagisto.shop.products.view.make-it-a-gift.drawer.toggle.after') !!}
                                        </div>
                                    </x-slot>

                                    <x-slot:header class="border-b border-gray-300">
                                        <div class="flex gap-2 items-center text-black text-lg font-semibold uppercase leading-normal relative" >
                                            <img src="{{ asset('widian-assets/images/gift.svg') }}" alt="Gift Icon" />
                                            <span class="mt-1.5">@lang('shop::app.products.view.gift.make-it-a-gift')</span>
                                        </div>
                                    </x-slot>

                                    <x-slot:content class="!px-9">
                                        <div class="py-12 space-y-8">
                                            <div class="space-y-4">
                                                <h3 class="text-base font-medium text-gray-800">@lang('shop::app.products.view.gift.choose-your-exclusive-amouage-gift-wrap')</h3>

                                                <div class="grid grid-cols-3 gap-8">
                                                    <div @click="draftSelectedWrapColor = 'black'" :class="{'border-secondary border': draftSelectedWrapColor === 'black'}" class="border rounded-md p-6 flex flex-col items-center cursor-pointer transition-all hover:border-secondary">
                                                        <div class="w-16 h-16 bg-black mb-2"></div>
                                                        <span class="text-sm text-center">@lang('shop::app.products.view.gift.black')</span>
                                                    </div>

                                                    <div @click="draftSelectedWrapColor = 'white'" :class="{'border-secondary border': draftSelectedWrapColor === 'white'}" class="border rounded-md p-6 flex flex-col items-center cursor-pointer transition-all hover:border-secondary">
                                                        <div class="w-16 h-16 bg-gray-100 mb-2"></div>
                                                        <span class="text-sm text-center">@lang('shop::app.products.view.gift.white')</span>
                                                    </div>

                                                    <div @click="draftSelectedWrapColor = 'cashmere'" :class="{'border-secondary border': draftSelectedWrapColor === 'cashmere'}" class="border rounded-md p-6 flex flex-col items-center cursor-pointer transition-all hover:border-secondary">
                                                        <div class="w-16 h-16 bg-[#E8DFD1] mb-2"></div>
                                                        <span class="text-sm text-center">@lang('shop::app.products.view.gift.cashmere')</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="space-y-4">
                                                <div class="flex items-center gap-2">
                                                    <label class="relative flex cursor-pointer items-center rounded-full" for="add_gift_message_draft" data-ripple-dark="true">
                                                        <input type="checkbox" v-model="draftAddGiftMessage" id="add_gift_message_draft" class="before:content[''] peer relative h-4 w-4 cursor-pointer appearance-none rounded-[2px] border border-secondary transition-all before:absolute before:top-2/4 before:left-2/4 before:block checked:border-secondary checked:bg-secondary checked:before:bg-secondary"/>
                                                        <div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
                                                            <img src="{{ asset('widian-assets/images/tick.svg') }}" class="h-2 w-2" alt="">
                                                        </div>
                                                    </label>
                                                    <label for="add_gift_message_draft" class="text-base font-medium text-gray-800 mt-1">@lang('shop::app.products.view.gift.personalise-your-gift-with-a-special-message')</label>
                                                </div>

                                                <transition name="accordion" @enter="startTransition" @leave="endTransition">
                                                    <div v-if="draftAddGiftMessage" class="space-y-4">
                                                        <div class="space-y-2">
                                                            <label for="recipient_name_draft" class="block text-sm font-medium text-gray-700">@lang('shop::app.products.view.gift.to')</label>
                                                            <input type="text" id="recipient_name_draft" v-model="draftGiftMessage.to" placeholder="@lang('shop::app.products.view.gift.recipient-name')"
                                                                class="w-full px-6 py-2 xl:pt-3 rounded border border-[#707070] focus:outline-none bg-white placeholder:text-dark-gray-2"
                                                            >
                                                        </div>

                                                        <div class="space-y-2">
                                                            <label for="sender_name_draft" class="block text-sm font-medium text-gray-700">@lang('shop::app.products.view.gift.from')</label>
                                                            <input type="text" id="sender_name_draft" placeholder="@lang('shop::app.products.view.gift.sender-name')" v-model="draftGiftMessage.from"
                                                                class="w-full px-6 py-2 xl:pt-3 rounded border border-[#707070] focus:outline-none bg-white placeholder:text-dark-gray-2"
                                                            >
                                                        </div>

                                                        <div class="space-y-2">
                                                            <label for="gift_message_draft" class="block text-sm font-medium text-gray-700">@lang('shop::app.products.view.gift.message')</label>
                                                            <textarea id="gift_message_draft" rows="4" v-model="draftGiftMessage.message" placeholder="@lang('shop::app.products.view.gift.write-your-gift-message')"
                                                                class="w-full px-6 py-2 xl:pt-3 rounded border border-[#707070] focus:outline-none bg-white placeholder:text-dark-gray-2"
                                                            ></textarea>
                                                        </div>
                                                    </div>
                                                </transition>
                                            </div>
                                        </div>
                                    </x-slot>

                                    <x-slot:footer class="!shadow-none">
                                        <div class="flex justify-end items-center">
                                            <button @click="saveGiftDetails" type="button" class="px-6 py-3 bg-black hover:bg-secondary text-white rounded-md transition duration-300">
                                                @lang('shop::app.products.view.gift.apply')
                                            </button>
                                        </div>
                                    </x-slot>
                                </x-shop::drawer>

                                <button type="submit" class="bg-black hover:bg-secondary duration-300 flex items-center justify-center w-full text-white text-lg font-bold uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12">
                                    <span class="mt-1">@lang('shop::app.products.view.add-to-cart')</span>
                                </button>
                            </div>

                            <div class="mt-10 space-y-10">
                                <details class="w-full border-b border-light-gray group">
                                    <summary class="flex items-center justify-between w-full cursor-pointer py-4 uppercase">
                                        <span class="text-black text-lg font-bold leading-normal">@lang('shop::app.products.view.product-description')</span>
                                        <img src="{{ asset('widian-assets/images/plus.svg') }}" alt="Expand" class="block group-open:hidden w-4 h-4"/>
                                        <img src="{{ asset('widian-assets/images/minus.svg') }}" alt="Collapse" class="hidden group-open:block w-4 h-4"/>
                                    </summary>
                                    <div class="pt-2 pb-5 text-dark-gray-2">
                                        {!! $product->description !!}
                                    </div>
                                </details>

                                {!! isset($notesIngredients) && !empty($notesIngredients) ? $notesIngredients->options['html'] : '' !!}
                            </div>
                        </div>
                    </section>
                </form>
            </x-shop::form>

            @if(isset($product->perfume_notes) && !empty($product->perfume_notes))
                <div data-aos="zoom-in" data-aos-duration="800" data-aos-easing="ease-out">{!! $product->perfume_notes !!}</div>
            @endif

            @if(isset($product->perfumer_name) && !empty($product->perfumer_name))
                <section class="perfumer-section bg-primary-dark px-5 item-center">
                    <div class="flex max-sm:flex-col max-md:items-center sm:justify-between max-w-7xl mx-auto gap-32 lg:gap-64 xl:gap-64 items-center">
                        <div class="w-full">
                            <p class="text-black text-3xl md:text-4xl font-normal leading-10 mt-12" data-aos="fade-down" data-aos-duration="600" data-aos-easing="ease-in">@lang('shop::app.products.view.perfumer')</p>
                            <h1 class="text-black text-3xl md:text-4xl font-medium font-playfair leading-10 whitespace-nowrap mt-3" data-aos="fade-up" data-aos-duration="600" data-aos-easing="ease-out">{{ $product->perfumer_name }}</h1>
                            @if(isset($product->perfumer_quote) && !empty($product->perfumer_quote))
                                <p class="text-black font-normal text-base mt-6 mb-12" data-aos="fade-up" data-aos-duration="600" data-aos-easing="ease-out">{{ $product->perfumer_quote }}</p>
                            @endif
                        </div>

                        @if(isset($product->perfumer_images) && !empty($product->perfumer_images) && $product->perfumer_images->first())
                            <div class="w-full">
                                <img class="w-full h-full object-cover" src="{{ $product->perfumer_images->first()->url }}" alt="" data-aos="zoom-in" data-aos-duration="600" data-aos-easing="ease-out"/>
                            </div>
                        @endif
                    </div>
                </section>
            @endif
        </script>

        <script type="text/x-template" id="v-you-may-also-like-products-template">
            <section v-if="products.length > 0" class="bg-primary pt-[120px]">
                <h1 class="text-secondary text-center text-3xl md:text-4xl xl:text-5xl font-medium font-playfair leading-[50px] mb-[60px]" data-aos="zoom-in" data-aos-duration="600" data-aos-easing="ease-out">@lang('shop::app.products.view.you-may-also-like')</h1>

                <div class="product-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 max-w-[1920px] mx-auto">
                    <template v-if="isLoadingYouMayAlsoLike">
                        <x-shop::shimmer.products.cards.grid count="4" />
                    </template>
                    <template v-else>
                        <div v-for="product in products" :key="product.id" class="p-5 text-center product-card relative group">
                            <span v-if="product.is_new" class="absolute w-12 h-6 flex items-center justify-center pt-[3px] top-5 left-5 bg-secondary text-white text-xs font-bold rounded-full z-10">
                                @lang('shop::app.categories.new')
                            </span>

                            <ul class="absolute right-5 top-5 space-y-3 hidden group-hover:block transition ease-in-out duration-200 z-10">
                                <li class="w-10 h-10 bg-secondary rounded-full flex justify-center items-center cursor-pointer">
                                    <img src="{{ asset('widian-assets/images/quick-view.svg') }}" alt="quick-view" />
                                </li>

                                @if (core()->getConfigData('sales.checkout.shopping_cart.cart_page'))
                                    {!! view_render_event('bagisto.shop.components.products.card.add_to_cart.before') !!}

                                    <button class="w-10 h-10 bg-secondary rounded-full flex justify-center items-center cursor-pointer"
                                        :disabled="! product.is_saleable || isAddingToCart"
                                        @click="addToCart(product)"
                                    >
                                        <img src="{{ asset('widian-assets/images/cart-icon.svg') }}" alt="cart" />
                                    </button>

                                    {!! view_render_event('bagisto.shop.components.products.card.add_to_cart.after') !!}
                                @endif
                            </ul>

                            <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)">
                                <div class="h-[264px] sm:h-[459px] relative w-full overflow-hidden mt-8" data-aos="zoom-out" data-aos-duration="800" data-aos-easing="ease-out">
                                    <img
                                    :src="product.cover_images[0]?.url ?? product.base_image.original_image_url"
                                    :alt="product.name"
                                    class="absolute inset-0 w-full h-full object-contain sm:object-cover transition-opacity duration-300 ease-in-out opacity-100 group-hover:opacity-0"
                                    />

                                    <img
                                    :src="product.hover_images[0]?.url ?? product.base_image.original_image_url"
                                    :alt="product.name"
                                    class="absolute inset-0 w-full h-full object-contain sm:object-cover transition-opacity duration-300 ease-in-out opacity-0 group-hover:opacity-100"
                                    />
                                </div>
                            </a>

                            <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)" class="text-center text-black text-xl md:text-2xl font-normal font-playfair uppercase leading-loose tracking-wider" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="200">
                                @{{ product.name }}
                            </a>

                            <div class="flex items-center justify-center gap-3 flex-wrap" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="300">
                                <p class="text-center text-sm md:text-base text-[#3D3C3A] font-normal leading-snug tracking-wide">
                                    @{{ product.prices.final.formatted_price }}
                                </p>
                                <p v-if="product.prices.regular.formatted_price != product.prices.final.formatted_price" class="line-through text-center text-xs md:text-sm opacity-50 text-[#3D3C3A] font-normal leading-snug tracking-wide">
                                    @{{ product.prices.regular.formatted_price }}
                                </p>
                            </div>
                        </div>
                    </template>
                </div>
            </section>
        </script>

        <script type="module">
            app.component('v-product', {
                template: '#v-product-template',

                data() {
                    return {
                        isWishlist: Boolean("{{ (boolean) auth()->guard()->user()?->wishlist_items->where('channel_id', core()->getCurrentChannel()->id)->where('product_id', $product->id)->count() }}"),
                        isCustomer: '{{ auth()->guard('customer')->check() }}',
                        is_buy_now: 0,
                        isStoring: {
                            addToCart: false,
                            buyNow: false,
                        },
                        isGift: false,
                        appliedSelectedWrapColor: 'black',
                        appliedAddGiftMessage: false,
                        appliedGiftMessage: {
                            to: '',
                            from: '',
                            message: ''
                        },
                        draftSelectedWrapColor: 'black',
                        draftAddGiftMessage: false,
                        draftGiftMessage: {
                            to: '',
                            from: '',
                            message: ''
                        },
                        isCarouselLoaded: false,
                    }
                },

                mounted() {
                    this.$nextTick(() => {
                        setTimeout(() => {
                            $('.product-image-carousel').owlCarousel({
                                rtl: false,
                                loop: true,
                                margin: 30,
                                nav: true,
                                autoplay: false,
                                mouseDrag: true,
                                dots: false,
                                items: 1,
                                startPosition: 0,
                                navText: [
                                    '<svg class="rotate-180" width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" /></svg>',
                                    '<svg width="13" height="20" viewBox="0 0 13 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.650024 17.6333L8.28336 10L0.650024 2.35L3.00002 0L13 10L3.00002 20L0.650024 17.6333Z" fill="#CCCCCC" /></svg>'
                                ]
                            });
                            this.isCarouselLoaded = true;
                        }, 3000);
                    });
                },

                methods: {
                    openGiftDrawer() {
                        this.draftSelectedWrapColor = this.appliedSelectedWrapColor;
                        this.draftAddGiftMessage = this.appliedAddGiftMessage;
                        this.draftGiftMessage = { ...this.appliedGiftMessage };
                        this.$refs.giftDrawer.open();
                    },

                    addToCart(params) {
                        const operation = this.is_buy_now ? 'buyNow' : 'addToCart';

                        this.isStoring[operation] = true;

                        let formData = new FormData(this.$refs.formData);

                        this.ensureQuantity(formData);

                        formData.append("is_gift",this.isGift);
                        if(this.isGift){
                            formData.append("gift_wrap_color",this.appliedSelectedWrapColor);
                            formData.append("is_gift_message",this.appliedAddGiftMessage);

                            if(this.appliedAddGiftMessage){
                                formData.append("gift_wrap_to", this.appliedGiftMessage.to);
                                formData.append("gift_wrap_from", this.appliedGiftMessage.from);
                                formData.append("gift_wrap_message", this.appliedGiftMessage.message);
                            }
                        }

                        this.$axios.post('{{ route("shop.api.checkout.cart.store", app()->getLocale()) }}', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                }
                            })
                            .then(response => {
                                if (response.data.message) {
                                    this.$emitter.emit('update-mini-cart', response.data.data);

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });

                                    if (response.data.redirect) {
                                        window.location.href = response.data.redirect;
                                    }
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isStoring[operation] = false;
                            })
                            .catch(error => {
                                this.isStoring[operation] = false;

                                this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.message });
                            });
                    },

                    addToWishlist() {
                        if (this.isCustomer) {
                            this.$axios.post('{{ route('shop.api.customers.account.wishlist.store', app()->getLocale()) }}', {
                                    product_id: "{{ $product->id }}"
                                })
                                .then(response => {
                                    this.isWishlist = ! this.isWishlist;

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                                })
                                .catch(error => {});
                        } else {
                            window.location.href = "{{ route('shop.customer.session.index', app()->getLocale())}}";
                        }
                    },

                    ensureQuantity(formData) {
                        if (!formData.has('quantity')) {
                            formData.append('quantity', 1);
                        }
                    },

                    saveGiftDetails() {
                        this.appliedSelectedWrapColor = this.draftSelectedWrapColor;
                        this.appliedAddGiftMessage = this.draftAddGiftMessage;
                        this.appliedGiftMessage = { ...this.draftGiftMessage };

                        this.isGift = true;
                        this.$refs.giftDrawer.close();
                    },

                    startTransition(el) {
                        el.style.maxHeight = '0';
                        el.style.opacity = '0';
                        el.offsetHeight;
                        this.$nextTick(() => {
                            el.style.maxHeight = el.scrollHeight + 'px';
                            el.style.opacity = '1';
                        });
                    },

                    endTransition(el) {
                        el.style.maxHeight = el.scrollHeight + 'px';
                        el.style.opacity = '1';
                        el.offsetHeight;
                        this.$nextTick(() => {
                            el.style.maxHeight = '0';
                            el.style.opacity = '0';
                        });
                    },

                    removeGiftDetails() {
                        this.$emitter.emit('open-confirm-modal', {
                            agree: () => {
                                this.draftSelectedWrapColor = 'black';
                                this.draftAddGiftMessage = false;
                                this.draftGiftMessage = {
                                    to: '',
                                    from: '',
                                    message: ''
                                }
                                this.appliedSelectedWrapColor = 'black';
                                this.appliedAddGiftMessage = false;
                                this.appliedGiftMessage = {
                                    to: '',
                                    from: '',
                                    message: ''
                                }
                                this.isGift = false;
                            }
                        });
                    },
                },
            });

            app.component('you-may-also-like-products', {
                template: '#v-you-may-also-like-products-template',

                data() {
                    return {
                        isLoadingYouMayAlsoLike: false,
                        products: [],
                        isAddingToCart: false,
                    };
                },

                mounted() {
                    this.getProducts();
                },

                methods: {
                    getProducts() {
                        this.isLoadingYouMayAlsoLike = true;
                        this.$axios.get(`{{ route('shop.api.products.related.index', [app()->getLocale(), $product->id])}}`)
                            .then(response => {
                                this.isLoadingYouMayAlsoLike = false;

                                this.products = response.data.data;
                            }).catch(error => {
                                console.log(error);
                            });
                    },

                    addToCart(product) {
                        this.isAddingToCart = true;

                        this.$axios.post('{{ route("shop.api.checkout.cart.store", app()->getLocale()) }}', {
                                'quantity': 1,
                                'product_id': product.id,
                            })
                            .then(response => {
                                if (response.data.message) {
                                    this.$emitter.emit('update-mini-cart', response.data.data );

                                    this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                                } else {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                                }

                                this.isAddingToCart = false;
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                                if (error.response.data.redirect_uri) {
                                    window.location.href = error.response.data.redirect_uri;
                                }

                                this.isAddingToCart = false;
                            });
                    },
                },
            });
        </script>
    @endPushOnce
</x-shop::layouts>
