{!! view_render_event('bagisto.shop.components.layouts.header.desktop.top.before') !!}

<v-topbar></v-topbar>

{!! view_render_event('bagisto.shop.components.layouts.header.desktop.top.after') !!}

@pushOnce('scripts')
    @if(core()->getConfigData('general.content.header_offer.marquee_container') == 1)
        <script
            type="text/x-template"
            id="v-topbar-template"
        >
            <div class="flex w-full items-center justify-center border border-b border-l-0 border-r-0 border-t-0 px-16 py-3 bg-secondary">
                <p class="text-white text-sm text-center">
                    {{ core()->getConfigData('general.content.header_offer.title') }}

                    <a
                        href="{{ core()->getConfigData('general.content.header_offer.redirection_link') }}"
                        class="underline"
                        role="button"
                    >
                        {{ core()->getConfigData('general.content.header_offer.redirection_title') }}
                    </a>
                </p>
            </div>
        </script>
    @endif

    <script
        type="text/x-template"
        id="v-currency-switcher-template"
    >
        <div class="my-2.5 grid gap-1 overflow-auto max-md:my-0 sm:max-h-[500px]">
            <div
                class="flex cursor-pointer items-center gap-2.5 px-5 py-2 text-xs hover:bg-primary"
                v-for="currency in currencies"
                :class="{'bg-primary': currency.code == '{{ core()->getCurrentCurrencyCode() }}'}"
                @click="change(currency)"
            >
                <img
                    :src="currency.logo_url || '{{ bagisto_asset('images/default-language.svg') }}'"
                    width="16"
                    height="16"
                />

                <span class="leading-none mt-0.5">@{{ currency.code }}</span>
            </div>
        </div>
    </script>

    <script
        type="text/x-template"
        id="v-locale-switcher-template"
    >
        <div class="my-2.5 grid gap-1 overflow-auto max-md:my-0 sm:max-h-[500px]">
            <div
                class="flex cursor-pointer items-center gap-2.5 px-5 py-2 text-xs hover:bg-primary"
                :class="{'bg-primary': locale.code == '{{ app()->getLocale() }}'}"
                v-for="locale in locales"
                @click="change(locale)"
            >
                <img
                    :src="locale.logo_url || '{{ bagisto_asset('images/default-language.svg') }}'"
                    width="16"
                    height="16"
                />

                <span class="leading-none mt-0.5">@{{ locale.name }}</span>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-topbar', {
            template: '#v-topbar-template',

            data() {
                return {
                    localeToggler: '',

                    currencyToggler: '',
                };
            },
        });

        app.component('v-currency-switcher', {
            template: '#v-currency-switcher-template',

            data() {
                return {
                    currencies: @json(core()->getCurrentChannel()->currencies),
                };
            },

            methods: {
                change(currency) {
                    let url = new URL(window.location.href);

                    url.searchParams.set('currency', currency.code);

                    window.location.href = url.href;
                }
            }
        });

        app.component('v-locale-switcher', {
            template: '#v-locale-switcher-template',

            data() {
                return {
                    locales: @json(core()->getCurrentChannel()->locales()->orderBy('name')->get()),
                };
            },

            methods: {
                change(locale) {
                    let currentPath = window.location.pathname;
                    let currentLocale = '{{ app()->getLocale() }}';

                    if (currentPath.startsWith('/' + currentLocale + '/')) {
                        currentPath = currentPath.substring(('/' + currentLocale).length);
                    } else if (currentPath === '/' + currentLocale) {
                        currentPath = '/';
                    }

                    if (!currentPath.startsWith('/')) {
                        currentPath = '/' + currentPath;
                    }

                    let newPath = '/' + locale.code + (currentPath === '/' ? '' : currentPath);

                    let search = window.location.search;
                    let hash = window.location.hash;

                    window.location.href = newPath + search + hash;
                }
            }
        });
    </script>
@endPushOnce
