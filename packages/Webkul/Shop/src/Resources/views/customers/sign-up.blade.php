<!-- SEO Meta Content -->
@push('meta')
    <meta
        name="description"
        content="@lang('shop::app.customers.signup-form.page-title')"
    />

    <meta
        name="keywords"
        content="@lang('shop::app.customers.signup-form.page-title')"
    />
@endPush

@php
    $isRegisterFromCheckout = url()->previous() == route('shop.checkout.cart.index', app()->getLocale());
@endphp

<x-shop::layouts
    :has-header="true"
    :has-feature="false"
    :has-footer="true"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.signup-form.page-title')
    </x-slot>

    <section class="grid grid-cols-1 lg:grid-cols-2 items-center">
        <div class="h-full">
          <img src="{{ asset('widian-assets/images/register.jpg') }}" class="w-full h-full object-cover" alt="">
        </div>

        <div class="px-5 lg:px-10 xl:px-20 2xl:px-36 max-2xl:py-10">
          <h6 class="font-playfair uppercase max-md:text-md text-lg text-black leading-6 font-medium mb-4">
            @lang('shop::app.customers.form-welcome-text')
          </h6>

          <h1 class="text-2xl 2xl:text-4xl leading-[42px] font-medium font-playfair text-secondary">
            @lang('shop::app.customers.signup-form.form-signup-text')
          </h1>

          <x-shop::form :action="route('shop.customers.register.store', app()->getLocale())" class="py-[50px]">
            {!! view_render_event('bagisto.shop.customers.signup_form_controls.before') !!}

            <input type="hidden" name="isRegisterFromCheckout" value="{{ $isRegisterFromCheckout }}">

            <x-shop::form.control-group>
                <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                    @lang('shop::app.customers.signup-form.first-name')
                </x-shop::form.control-group.label>

                <x-shop::form.control-group.control
                    type="text"
                    class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                    name="first_name"
                    rules="required"
                    :value="old('first_name')"
                    :label="trans('shop::app.customers.signup-form.first-name')"
                    :placeholder="trans('shop::app.customers.signup-form.first-name')"
                    :aria-label="trans('shop::app.customers.signup-form.first-name')"
                    aria-required="true"
                />

                <x-shop::form.control-group.error control-name="first_name" />
            </x-shop::form.control-group>

            {!! view_render_event('bagisto.shop.customers.signup_form.first_name.after') !!}

            <x-shop::form.control-group>
                <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                    @lang('shop::app.customers.signup-form.last-name')
                </x-shop::form.control-group.label>

                <x-shop::form.control-group.control
                    type="text"
                    class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                    name="last_name"
                    rules="required"
                    :value="old('last_name')"
                    :label="trans('shop::app.customers.signup-form.last-name')"
                    :placeholder="trans('shop::app.customers.signup-form.last-name')"
                    :aria-label="trans('shop::app.customers.signup-form.last-name')"
                    aria-required="true"
                />

                <x-shop::form.control-group.error control-name="last_name" />
            </x-shop::form.control-group>

            {!! view_render_event('bagisto.shop.customers.signup_form.last_name.after') !!}

            <x-shop::form.control-group>
                <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                    @lang('shop::app.customers.signup-form.email')
                </x-shop::form.control-group.label>

                <x-shop::form.control-group.control
                    type="email"
                    class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                    name="email"
                    rules="required|email"
                    :value="old('email')"
                    :label="trans('shop::app.customers.signup-form.email')"
                    :placeholder="trans('shop::app.customers.signup-form.email')"
                    :aria-label="trans('shop::app.customers.signup-form.email')"
                    aria-required="true"
                />

                <x-shop::form.control-group.error control-name="email" />
            </x-shop::form.control-group>

            {!! view_render_event('bagisto.shop.customers.signup_form.email.after') !!}

            <x-shop::form.control-group class="mb-6">
                <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                    @lang('shop::app.customers.signup-form.password')
                </x-shop::form.control-group.label>

                <x-shop::form.control-group.control
                    type="password"
                    class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                    name="password"
                    rules="required|min:6"
                    :value="old('password')"
                    :label="trans('shop::app.customers.signup-form.password')"
                    placeholder="*************"
                    ref="password"
                    :aria-label="trans('shop::app.customers.signup-form.password')"
                    aria-required="true"
                />

                <x-shop::form.control-group.error control-name="password" />
            </x-shop::form.control-group>

            {!! view_render_event('bagisto.shop.customers.signup_form.password.after') !!}

            <x-shop::form.control-group class="mb-0">
                <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                    @lang('shop::app.customers.signup-form.confirm-pass')
                </x-shop::form.control-group.label>

                <x-shop::form.control-group.control
                    type="password"
                    class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                    name="password_confirmation"
                    rules="confirmed:@password"
                    value=""
                    :label="trans('shop::app.customers.signup-form.password')"
                    placeholder="*************"
                    :aria-label="trans('shop::app.customers.signup-form.confirm-pass')"
                    aria-required="true"
                />

                <x-shop::form.control-group.error control-name="password_confirmation" />
            </x-shop::form.control-group>

            {!! view_render_event('bagisto.shop.customers.signup_form.password_confirmation.after') !!}

            <div class="flex gap-2 md:gap-4 items-center py-8">
                <label class="flex items-center cursor-pointer relative">
                  <input type="checkbox"
                    class="peer h-6 w-6 cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent rounded-[2px]"
                    name="terms_conditions" />
                  <span
                    class="absolute block text-white opacity-0 peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <svg class="w-5 h-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                      stroke-width="3">
                      <path d="M5 13L9 17L19 7" />
                    </svg>
                  </span>
                </label>

                <span class="text-xs md:text-sm lg:text-base text-dark-gray">
                    @lang('shop::app.customers.signup-form.terms-conditions')
                </span>

                <x-shop::form.control-group.error control-name="terms_conditions" />
            </div>

            <button type="submit" class="bg-secondary hover:bg-black duration-300 min-w-[150px] sm:min-w-[300px] py-3 sm:py-5 text-base md:text-xl leading-6 rounded-[8px] md:rounded-[10px] text-white ltr:ml-0 rtl:mr-0">
                @lang('shop::app.customers.signup-form.button-title')
            </button>

            {!! view_render_event('bagisto.shop.customers.signup_form_controls.after') !!}

        </x-shop::form>

        <p class="flex items-center text-base text-dark-gray leading-[22px]">
            <span>
                @lang('shop::app.customers.signup-form.account-exists')
            </span>
            <a class="ps-1 font-bold"
                href="{{ route('shop.customer.session.index', app()->getLocale()) }}"
            >
                @lang('shop::app.customers.signup-form.login')
            </a>
        </p>
        </div>
    </section>
</x-shop::layouts>
