<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="@lang('shop::app.customers.login-form.page-title')"/>

    <meta name="keywords" content="@lang('shop::app.customers.login-form.page-title')"/>
@endPush

@php
    $isLoginFromCheckout = url()->previous() == route('shop.checkout.cart.index', app()->getLocale());
@endphp

<x-shop::layouts
    :has-header="true"
    :has-feature="false"
    :has-footer="true"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.login-form.page-title')
    </x-slot>

    <section class="grid grid-cols-1 lg:grid-cols-2 items-center ">
        <div class="h-full">
            <img src="{{ asset('widian-assets/images/register.jpg') }}" class="w-full h-full object-cover" alt="">
        </div>

        <div class="px-5 lg:px-10 xl:px-20 2xl:px-36 max-2xl:py-10 order-1 sm:order-2">
            <h6 class="font-playfair uppercase max-md:text-md text-lg text-black leading-6 font-medium mb-4">
                @lang('shop::app.customers.form-welcome-text')
            </h6>

            <v-sign-in-form>
                <div class="py-[50px]">
                    <x-shop::shimmer.form.control-group count="1" />
                </div>
            </v-sign-in-form>

            <p class="flex items-center text-base text-dark-gray leading-[22px]">
                <span>
                    @lang('shop::app.customers.login-form.account-exists')
                </span>
                <a class="ps-1 font-bold text-dark-gray hover:text-secondary transition duration-500"
                    href="{{ route('shop.customers.register.index', app()->getLocale()) }}"
                >
                    @lang('shop::app.customers.login-form.register-now')
                </a>
            </p>
        </div>
    </section>

    @pushOnce('scripts')
        <script type="text/x-template" id="v-sign-in-form-template">
            <h1 v-if="loginForm.loginWithPassword" class="text-3xl 2xl:text-4xl leading-[42px] font-medium font-playfair text-secondary">
                @lang('shop::app.customers.login-form.title')
            </h1>
            <h1 v-else class="text-3xl 2xl:text-4xl leading-[42px] font-medium font-playfair text-secondary">
                @lang('shop::app.customers.login-form.title-otp')
            </h1>

            <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
                <form ref="formData" @submit="handleSubmit($event, signIn)" class="py-[50px]" autocomplete="off">
                    {!! view_render_event('bagisto.shop.customers.login_form_controls.before') !!}

                    <x-shop::form.control-group.control
                        type="hidden"
                        class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                        name="login_with_password"
                        v-model="loginForm.loginWithPassword"
                    />

                    <!-- Email -->
                    <x-shop::form.control-group class="text-base mb-5">
                        <x-shop::form.control-group.label class="text-black leading-[22px] inline-block">
                            @lang('shop::app.customers.login-form.email')
                        </x-shop::form.control-group.label>

                        <x-shop::form.control-group.control
                            type="email"
                            class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                            name="email"
                            rules="required|email"
                            v-model="loginForm.email"
                            :label="trans('shop::app.customers.login-form.email')"
                            :placeholder="trans('shop::app.customers.login-form.email-address')"
                            :aria-label="trans('shop::app.customers.login-form.email')"
                            aria-required="true"
                        />

                        <x-shop::form.control-group.error control-name="email" />
                    </x-shop::form.control-group>

                    <div v-if="step == 'email'">
                        <div class="flex gap-2 md:gap-3 items-center pt-0 pb-5">
                            <label class="relative flex cursor-pointer items-center rounded-full">
                                <input type="checkbox" id="loginWithPassword" v-model="loginForm.loginWithPassword" class="before:content[''] peer relative h-4 w-4 cursor-pointer appearance-none rounded-[2px] border border-secondary transition-all before:absolute before:top-2/4 before:left-2/4 before:block checked:border-secondary checked:bg-secondary checked:before:bg-secondary" />

                                <div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
                                    <img src="{{ asset('widian-assets/images/tick.svg') }}" class="h-2 w-2" alt="">
                                </div>
                            </label>
                            <label for="loginWithPassword" class="text-xs md:text-sm lg:text-base text-dark-gray cursor-pointer">@lang('shop::app.customers.login-form.login-with-password')</label>
                        </div>

                        <!-- Password -->
                        <div v-if="loginForm.loginWithPassword">
                            <x-shop::form.control-group class="text-base mb-5">
                                <x-shop::form.control-group.label class="text-black leading-[22px] inline-block">
                                    @lang('shop::app.customers.login-form.password')
                                </x-shop::form.control-group.label>

                                <x-shop::form.control-group.control
                                    type="password"
                                    class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                                    id="password"
                                    name="password"
                                    rules="required|min:6"
                                    v-model="loginForm.password"
                                    :label="trans('shop::app.customers.login-form.password')"
                                    placeholder="*************"
                                    :aria-label="trans('shop::app.customers.login-form.password')"
                                    aria-required="true"
                                />

                                <x-shop::form.control-group.error control-name="password" />
                            </x-shop::form.control-group>

                            <div class="flex gap-2 md:gap-4 items-center pt-0 pb-5">
                                <span class="text-xs md:text-sm lg:text-base text-dark-gray">
                                    <a href="{{ route('shop.customers.forgot_password.create', app()->getLocale()) }}" class="text-dark-gray hover:text-secondary">
                                        @lang('shop::app.customers.login-form.forgot-pass')
                                    </a>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div v-else class="mb-5">
                        <x-shop::form.control-group class="text-base mb-2">
                            <x-shop::form.control-group.control
                                type="text"
                                class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                                name="otp"
                                rules="required|numeric"
                                v-model="loginForm.otp"
                                :label="trans('shop::app.customers.login-form.otp')"
                                :placeholder="trans('shop::app.customers.login-form.enter-otp')"
                                :aria-label="trans('shop::app.customers.login-form.otp')"
                                aria-required="true"
                            />

                            <x-shop::form.control-group.error control-name="otp" />
                        </x-shop::form.control-group>
                        <div class="flex justify-end">
                            <span @click="sendOtp()" class="text-secondary hover:underline cursor-pointer">@lang('shop::app.customers.login-form.resend-otp')</span>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button v-if="loginForm.loginWithPassword || step == 'otp'" type="submit" class="bg-secondary hover:bg-black duration-300 min-w-[150px] sm:min-w-[300px] py-3 sm:py-5 text-base md:text-xl leading-6 rounded-[8px] md:rounded-[10px] text-white ltr:ml-0 rtl:mr-0">
                        @{{  step == 'otp' ? `@lang('shop::app.customers.login-form.button-title-otp')` : `@lang('shop::app.customers.login-form.button-title')` }}
                    </button>
                    <button v-else type="button" @click="sendOtp()" class="bg-secondary hover:bg-black duration-300 min-w-[150px] sm:min-w-[300px] py-3 sm:py-5 text-base md:text-xl leading-6 rounded-[8px] md:rounded-[10px] text-white ltr:ml-0 rtl:mr-0">
                        @lang('shop::app.customers.login-form.button-title-otp')
                    </button>
                </form>
            </x-shop::form>
        </script>
        <script type="module">
            app.component('v-sign-in-form', {
                template: '#v-sign-in-form-template',

                data() {
                    return {
                        isLoading: false,
                        loginForm: {
                            loginWithPassword: false,
                            email: '',
                            password: '',
                            otp: '',
                            isLoginFromCheckout: @json($isLoginFromCheckout),
                        },
                        step: 'email',
                    };
                },

                methods: {
                    sendOtp() {
                        this.isLoading = true;

                        this.$axios.post('{{ route("shop.api.customers.send-otp", app()->getLocale()) }}', { 'email': this.loginForm.email })
                            .then(response => {
                                this.$emitter.emit('add-flash', {
                                    type: 'success',
                                    message: response.data.message
                                });

                                this.step = 'otp';

                                this.isLoading = false;
                            })
                            .catch(error => {
                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: error.response.data.message
                                });

                                this.isLoading = false;
                            });
                    },
                    signIn() {
                        this.isLoading = true;

                        if (this.loginForm.loginWithPassword || this.step == 'otp') {
                            this.$axios.post('{{ route("shop.api.customers.session.create", app()->getLocale()) }}', this.loginForm)
                                .then(response => {
                                    if (response.data.redirect_url) {
                                        window.location.href = response.data.redirect_url;
                                    } else if (response.data.message) {
                                        this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                                    }

                                    this.isLoading = false;
                                })
                                .catch(error => {
                                    this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                                    this.isLoading = false;
                                });
                        } else {
                            this.sendOtp();
                        }
                    },
                },
            });
        </script>
    @endPushOnce
</x-shop::layouts>
