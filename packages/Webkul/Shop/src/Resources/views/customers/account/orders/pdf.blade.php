@inject('attibuteOption', 'Webkul\Attribute\Models\AttributeOption')

<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}"
    dir="{{ core()->getCurrentLocale()->direction }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta
        http-equiv="Cache-control"
        content="no-cache"
    >

    <meta
        http-equiv="Content-Type"
        content="text/html; charset=utf-8"
    />

    <title>@lang('shop::app.customers.account.orders.invoice-pdf.invoice')</title>

    <style>
        /* Set Century Gothic as the default font */
        body {
            font-family: "Century Gothic", sans-serif;
            margin: 0;
            padding: 0;
            color: #030000;
        }

        /* Set A4 size for print */
        @page {
            size: A4;
            margin: 2mm;
            /* Adjust margins as needed */
        }
    </style>
</head>

<body
    style="font-family: 'Century Gothic', sans-serif; margin: 0; padding: 0; font-size: 14px;">
    <!-- A4 content wrapper -->
    <div style="display: inline-table; width: 100%; height: fit-content; max-width: 210mm; padding: 4mm; box-sizing: border-box; background-color: #ffff; margin: 0 auto;">

        <!-- Header Section -->
        <div style="display: inline-table; width: 100%; text-align: center; margin-bottom: 0px; background-color: #FCF9F2; padding: 20px; padding-top: 30px; border-radius: 6px 6px 0 0;">
            <a href="{{ config('app.url') }}" target="_blank">
                @if (core()->getConfigData('sales.invoice_settings.pdf_print_outs.logo'))
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('storage/' . core()->getConfigData('sales.invoice_settings.pdf_print_outs.logo')))) }}" width="180"/>
                @else
                    <img src="data:image/png;base64,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" width="180"/>
                @endif
            </a>
        </div>

        <!-- Billed To and TAX INVOICE Details -->
        <div style="width: 100%; display: inline-table; justify-content: space-between; margin-bottom: 20px; background-color: #FCF9F2; padding: 20px 10px; padding-top: 0px; border-radius: 0 0 6px 6px; line-height: 1.5;">
            <!-- Left side - Billed To -->
            @if ($invoice->order->billing_address)
                <div style="width: 48%; padding: 10px; display: table-cell;">
                    <p style="color: #707070;">
                        @lang('shop::app.customers.account.orders.invoice-pdf.billed-to'):
                    </p>
                    <h3 style="font-size: 20px; margin-bottom: 10px; font-weight: bold; margin-top: 0;">
                        {{ Str::title($invoice->order->billing_address->name) }}
                    </h3>
                    <p style="color: #3D3C3A;">
                        {{ $invoice->order->billing_address->address }},<br />{{ $invoice->order->billing_address->postcode . ' ' . $invoice->order->billing_address->city }},<br />{{ $invoice->order->billing_address->state . ', ' . core()->country_name($invoice->order->billing_address->country) }}
                    </p>
                    <p>
                        @lang('shop::app.customers.account.orders.invoice-pdf.email'): <a href="mailto:{{ $invoice->order->billing_address->email }}" style="color: #3D3C3A; text-decoration: none;">{{ $invoice->order->billing_address->email }}</a>
                    </p>
                    <p>
                        @lang('shop::app.customers.account.orders.invoice-pdf.phone'): <a href="tel:{{ $invoice->order->billing_address->phone }}" style="color: #3D3C3A; text-decoration: none;">{{ $invoice->order->billing_address->phone }}</a>
                    </p>
                </div>
            @endif

            <!-- Right side - TAX INVOICE -->
            <div style="width: 48%; text-align: right; padding: 10px; display: table-cell;">
                <h3 style="font-size: 20px; margin-bottom: 10px; font-weight: bold; margin-top: 0;">@lang('shop::app.customers.account.orders.invoice-pdf.tax-invoice')</h3>
                <p>
                    <span style="color: #707070;">@lang('shop::app.customers.account.orders.invoice-pdf.invoice-no'):</span> <br />
                    <span style="font-size: 18px; font-weight: bold;">#{{ $invoice->increment_id ?? $invoice->id }}</span>
                </p>
                <p><span style="color: #707070;">@lang('shop::app.customers.account.orders.invoice-pdf.issued-on'):</span> <br /> {{ core()->formatDate($invoice->created_at, 'F d, Y') }}</p>
                <p><span style="color: #707070;">@lang('shop::app.customers.account.orders.invoice-pdf.payment-due'):</span> <br />{{ core()->formatDate($invoice->created_at, 'F d, Y') }}</p>
            </div>
        </div>

        <div style="display: inline-table; width: 100%; height: fit-content;">
            <!-- Table Section -->
            <div style="margin-top: 20px; width: 100%; font-size: 13px;">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.item-s')</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.size')</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.qty')</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.u-m')</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: right;">@lang('shop::app.customers.account.orders.invoice-pdf.u-price', ['currency' => core()->getBaseCurrencyCode()])</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: right;">@lang('shop::app.customers.account.orders.invoice-pdf.taxable-value', ['currency' => core()->getBaseCurrencyCode()])</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: right;">@lang('shop::app.customers.account.orders.invoice-pdf.vat-rate')</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: right;">@lang('shop::app.customers.account.orders.invoice-pdf.vat-amount', ['currency' => core()->getBaseCurrencyCode()])</th>
                            <th style="background-color: #fcf9f2; padding: 16px 4px; text-align: right;">@lang('shop::app.customers.account.orders.invoice-pdf.total', ['currency' => core()->getBaseCurrencyCode()])</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($invoice->items as $item)
                            @php
                                $productSize = $attibuteOption->find($item->product->size) ?? [];
                            @endphp
                            <tr style="{{ $loop->first ? 'border-top: 1px solid #3D3C3A;' : '' }}">
                                <td style="padding: 10px 4px;">
                                    {{ $item->name }}

                                    @if (isset($item->additional['attributes']))
                                        <div>
                                            @foreach ($item->additional['attributes'] as $attribute)
                                                <b>{{ $attribute['attribute_name'] }} : </b>{{ $attribute['option_label'] }}</br>
                                            @endforeach
                                        </div>
                                    @endif
                                </td>
                                <td style="padding: 10px 4px; color: #3D3C3A;">
                                    {{ isset($productSize) && !empty($productSize) && isset($productSize->admin_name) && !empty($productSize->admin_name) ? $productSize->admin_name : '-' }}
                                </td>
                                <td style="padding: 10px 4px; color: #3D3C3A;">{{ $item->qty }}</td>
                                <td style="padding: 10px 4px; color: #3D3C3A;">@lang('shop::app.customers.account.orders.invoice-pdf.pcs')</td>
                                <td style="padding: 8px; text-align: right; color: #3D3C3A;">{{ core()->formatPriceInvoice($item->price) }}</td>
                                <td style="padding: 8px; text-align: right; color: #3D3C3A;">{{ core()->formatPriceInvoice($item->base_total) }}</td>
                                <td style="padding: 8px; text-align: right; color: #3D3C3A;">{{ $item->tax_amount == 0 ? '0%' : '5%' }}</td>
                                <td style="padding: 8px; text-align: right; color: #3D3C3A;">{{ core()->formatPriceInvoice($item->tax_amount) }}</td>
                                <td style="padding: 8px; text-align: right;">{{ core()->formatPriceInvoice($item->base_total_incl_tax) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <!-- Amounts Section -->
            <div style="display: table; width: 100%; margin-top: 20px; text-align: right; font-size: 14px;" class="responsive-table amount-section">
                @if(isset($complementaryProducts) && !empty($complementaryProducts))
                    <table width="width: fit-content; margin-right: auto; margin-left: 0; border-spacing: 0; color: #3D3C3A; font-family: 'Century Gothic', sans-serif;">
                        <tbody>
                            @foreach ($complementaryProducts as $product)
                                <tr>
                                    <td>@lang('shop::app.customers.account.orders.invoice-pdf.complimentary') {{ $product['size'] . ' ' . $product['key'] }}</td>
                                </tr>
                                <tr>
                                    <td>{{ $product['value'] }}</td>
                                </tr>
                                <br />
                            @endforeach
                        </tbody>
                    </table>
                @endif

                <table style="width: fit-content; margin-left: auto; margin-right: 0; border-spacing: 0; color: #3D3C3A; font-family: 'Century Gothic', sans-serif;">
                    <tbody>
                        <tr>
                            <td style="padding: 10px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.subtotal')</td>
                            <td style="padding: 10px 4px;">:</td>
                            <td style="text-align: right; padding: 8px;">{{ core()->formatPriceInvoice($invoice->base_sub_total) }}</td>
                        </tr>
                        @if ($invoice->order->shipping_address)
                            <tr>
                                <td style="padding: 10px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.shipping', ['method' => ($invoice->order->shipping_method == 'free_free' ? __('shop::app.customers.account.orders.invoice-pdf.free-shipping') : __('shop::app.customers.account.orders.invoice-pdf.flat-rate'))])</td>
                                <td style="padding: 10px 4px;">:</td>
                                <td style="text-align: right; padding: 8px;">{{ core()->formatPriceInvoice($invoice->base_shipping_amount_incl_tax) }}</td>
                            </tr>
                        @endif
                        <tr>
                            <td style="padding: 10px 4px; text-align: left;">
                                @if($invoice->order && $invoice->order->coupon_code)
                                    @lang('shop::app.customers.account.orders.invoice-pdf.discount-promo-code', ['promo_code' => $invoice->order->coupon_code])
                                @else
                                    @lang('shop::app.customers.account.orders.invoice-pdf.discount')
                                @endif
                            </td>
                            <td style="padding: 10px 4px;">:</td>
                            <td style="text-align: right; padding: 8px;">{{ core()->formatPriceInvoice($invoice->base_discount_amount) }}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px 4px; text-align: left;">@lang('shop::app.customers.account.orders.invoice-pdf.tax-percentage', ['tax' => 5])</td>
                            <td style="padding: 10px 4px;">:</td>
                            <td style="text-align: right; padding: 8px;">{{ core()->formatPriceInvoice($invoice->base_tax_amount) }}</td>
                        </tr>
                        <tr
                            style="border-radius: 6px; overflow: hidden; background: #fcf9f2; color: #030000; font-size: 16px;">
                            <td
                                style="border-bottom-left-radius: 6px; border-top-left-radius: 6px; padding: 8px; text-align: left;">
                                <strong>@lang('shop::app.customers.account.orders.invoice-pdf.total', ['currency' => core()->getBaseCurrencyCode()])</strong></td>
                            <td style="padding: 10px 4px;">:</td>
                            <td
                                style="text-align: right; border-bottom-right-radius: 6px; border-top-right-radius: 6px; padding: 8px;">
                                <strong>{{ core()->formatPriceInvoice($invoice->base_grand_total) }}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- A4 FOOTER -->
        <div class="a4-footer" style="display: inline-table; width: 100%; clear: both; margin-top: 20px;">
            <!--FOOTER SECTION-->
            <div style="margin-top: 0; padding: 0; width: 100%;">
                @if (core()->getConfigData('general.contact.company.company_logo'))
                    <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('storage/' . core()->getConfigData('general.contact.company.company_logo')))) }}" width="330" style="max-width: 100%;"/>
                @else
                    <img src="data:image/png;base64,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" width="330" style="max-width: 100%;"/>
                @endif
                <p style="margin-top: 10px; margin-bottom: 0;">@lang('shop::app.customers.account.orders.invoice-pdf.trn-no', ['trn' => core()->getConfigData('general.contact.company.trn_no')])</p>
            </div>

            <!-- Contact Info Section -->
            <div style="margin-top: 20px; width: 100%; display: table; clear: both;" class="responsive-table contact-bottom">
                <table style="width: 100%; border-collapse: collapse; display: table;">
                    <thead style="margin-bottom: 10px;">
                        <tr>
                            <th style="text-align: left; padding: 0px 10px;">@lang('shop::app.customers.account.orders.invoice-pdf.corporate-office')</th>
                            <th style="text-align: left; padding: 0px 10px;">@lang('shop::app.customers.account.orders.invoice-pdf.sales-office')</th>
                            @if ($invoice->order->order_comments)
                                <th style="text-align: left; padding: 0px 10px;">@lang('shop::app.customers.account.orders.invoice-pdf.additional-notes')</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="vertical-align: baseline;">
                            <td style="padding: 10px;">
                                <p style="margin: 0;">
                                    <span style="color: #707070;">
                                        @lang('shop::app.customers.account.orders.invoice-pdf.address-location'):
                                    </span><br>
                                    {{ core()->getConfigData('general.contact.corporate-offices.address') }}
                                </p>
                                <p>
                                    <span style="color: #707070;">
                                        @lang('shop::app.customers.account.orders.invoice-pdf.email'):
                                    </span><br>
                                    <a href="mailto:{{ core()->getConfigData('general.contact.corporate-offices.email') }}" style="color: #3D3C3A; text-decoration: none;">{{ core()->getConfigData('general.contact.corporate-offices.email') }}</a>
                                </p>
                                <p style="margin: 0;">
                                    <span style="color: #707070;">@lang('shop::app.customers.account.orders.invoice-pdf.phone'):</span><br>
                                    <a href="tel:{{ core()->getConfigData('general.contact.corporate-offices.mobile_number') }}" style="color: #3D3C3A; text-decoration: none;">
                                        {{ core()->getConfigData('general.contact.corporate-offices.mobile_number') }}
                                    </a>
                                </p>
                            </td>
                            <td style="padding: 10px;">
                                <p style="margin: 0;">
                                    <span style="color: #707070;">@lang('shop::app.customers.account.orders.invoice-pdf.address-location'):</span><br>
                                    {{ core()->getConfigData('general.contact.sales-offices.address') }}
                                </p>
                                <p>
                                    <span style="color: #707070;">
                                        @lang('shop::app.customers.account.orders.invoice-pdf.email'):
                                    </span><br>
                                    <a href="mailto:{{ core()->getConfigData('general.contact.sales-offices.email') }}" style="color: #3D3C3A; text-decoration: none;">{{ core()->getConfigData('general.contact.sales-offices.email') }}</a>
                                </p>
                                <p style="margin: 0;">
                                    <span style="color: #707070;">
                                        @lang('shop::app.customers.account.orders.invoice-pdf.phone'):
                                    </span><br>
                                    <a href="tel:{{ core()->getConfigData('general.contact.sales-offices.mobile_number') }}" style="color: #3D3C3A; text-decoration: none;">
                                        {{ core()->getConfigData('general.contact.sales-offices.mobile_number') }}
                                    </a>
                                </p>
                            </td>
                            @if ($invoice->order->order_comments)
                                <td style="padding: 10px;">
                                    <p style="margin: 0;">
                                        <span style="color: #707070;">
                                            {{ $invoice->order->order_comments }}
                                        </span>
                                    </p>
                                </td>
                            @endif
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>

</html>
