<x-shop::layouts.account>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.account.return-refunds.view.page-title', ['order_id' => $order->increment_id])
    </x-slot>
    
    <!-- Breadcrumbs -->
    @section('breadcrumbs')
        <x-shop::breadcrumbs
            name="return-refunds"
            :entity="$order"
        />
    @endSection

    <div class="max-md:hidden">
        <x-shop::layouts.account.navigation />
    </div>

    <return-refunds/>

    @pushOnce('scripts')
        <script type="text/x-template" id="v-return-refunds-template">
            <div class="mx-4 flex-auto max-md:mx-6 max-sm:mx-4">
                <div class="p-[46px] shadow-[0_0_10px_rgba(0,0,0,0.1)] bg-white rounded-md m-2">
                    <div>
                        <h2 class="pb-10 text-2xl font-medium text-secondary max-md:text-xl max-sm:text-base ltr:ml-2.5 md:ltr:ml-0 rtl:mr-2.5 md:rtl:mr-0">Return and Refund</h2>
                    </div>
                    
                    <div v-if="step == 1" class="flex xl:flex-nowrap flex-wrap xl:gap-[72px] gap-10">
                        <div class="mb-6 flex-1">
                            <div class="space-y-1 mb-2">
                                <p class="text-sm font-medium">Order ID #1234</p>
                                <p class="text-dark-gray-2 text-sm">Delivered 03 June, 2025</p>
                            </div>        
                            <div class="space-y-1">
                                <p class="text-xs font-medium">Return Item</p>
                                <p class="text-dark-gray-2 text-xs">Eligible through 18 June, 2025</p>
                            </div>  
                            <!-- Product 1 -->
                            <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                                
                                <div class="space-y-5">
                                    <div class="flex gap-2.5  items-center justify-end">
                                        <label class="flex items-center cursor-pointer relative">
                                            <input id="gift-wrap" type="checkbox" class="peer h-5 w-5 rounded-[2px] cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent" name="is_gift">
                                            <span class="absolute h-3 w-3 block text-white opacity-0 bg-secondary peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                            </span>
                                        </label>
                                        <label for="gift-wrap" class="text-xs mt-1 text-dark-gray cursor-pointer"> Select </label>
                                    </div>
                                    <div class="flex items-center text-dark-gray flex justify-start border border-[#CCC] w-fit mt-4 sm:mt-0">
                                        <span class="cursor-pointer px-2 sm:w-10 sm:h-10 w-8 h-8 flex items-center justify-center" role="button" tabindex="0" aria-label="Decrease Quantity">
                                            <img src="http://127.0.0.1:8000/widian-assets/images/minus.svg" alt="minus" class="max-w-3 h-3">
                                        </span>
                                        <p class="bg-primary text-base flex items-center justify-center px-2 sm:w-10 sm:h-10 w-8 h-8">1</p>
                                        <span class="cursor-pointer flex items-center justify-center px-2 sm:w-10 sm:h-10 w-8 h-8" role="button" tabindex="0" aria-label="Increase Quantity">
                                            <img src="http://127.0.0.1:8000/widian-assets/images/plus.svg" alt="plus" class="max-w-3 h-3">
                                        </span>
                                    </div>
                                    
                                    
                                </div>
                            </div>
                            <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                                
                                <div class="space-y-5">
                                    
                                    <div class="flex gap-2.5  items-center justify-end">
                                        <label class="flex items-center cursor-pointer relative">
                                            <input id="gift-wrap" type="checkbox" class="peer h-5 w-5 rounded-[2px] cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent" name="is_gift">
                                            <span class="absolute h-3 w-3 block text-white opacity-0 bg-secondary peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                            </span>
                                        </label>
                                        <label for="gift-wrap" class="text-xs mt-1 text-dark-gray cursor-pointer"> Select </label>
                                    </div>
                                    <div class="flex items-center text-dark-gray flex justify-start border border-[#CCC] w-fit mt-4 sm:mt-0">
                                        <span class="cursor-pointer px-2 sm:w-10 sm:h-10 w-8 h-8 flex items-center justify-center" role="button" tabindex="0" aria-label="Decrease Quantity">
                                            <img src="http://127.0.0.1:8000/widian-assets/images/minus.svg" alt="minus" class="max-w-3 h-3">
                                        </span>
                                        <p class="bg-primary text-base flex items-center justify-center px-2 sm:w-10 sm:h-10 w-8 h-8">1</p>
                                        <span class="cursor-pointer flex items-center justify-center px-2 sm:w-10 sm:h-10 w-8 h-8" role="button" tabindex="0" aria-label="Increase Quantity">
                                            <img src="http://127.0.0.1:8000/widian-assets/images/plus.svg" alt="plus" class="max-w-3 h-3">
                                        </span>
                                    </div>
                                    
                                    
                                </div>
                            </div>
                            <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                                
                                <div class="space-y-5">
                                    <div class="flex gap-2.5  items-center justify-end">
                                        <label class="flex items-center cursor-pointer relative">
                                            <input id="gift-wrap" type="checkbox" class="peer h-5 w-5 rounded-[2px] cursor-pointer transition-all appearance-none border-2 border-secondary checked:bg-transparent" name="is_gift">
                                            <span class="absolute h-3 w-3 block text-white opacity-0 bg-secondary peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                            </span>
                                        </label>
                                        <label for="gift-wrap" class="text-xs mt-1 text-dark-gray cursor-pointer"> Select </label>
                                    </div>
                                    <div class="flex items-center text-dark-gray flex justify-start border border-[#CCC] w-fit mt-4 sm:mt-0">
                                        <span class="cursor-pointer px-2 sm:w-10 sm:h-10 w-8 h-8 flex items-center justify-center" role="button" tabindex="0" aria-label="Decrease Quantity">
                                            <img src="http://127.0.0.1:8000/widian-assets/images/minus.svg" alt="minus" class="max-w-3 h-3">
                                        </span>
                                        <p class="bg-primary text-base flex items-center justify-center px-2 sm:w-10 sm:h-10 w-8 h-8">1</p>
                                        <span class="cursor-pointer flex items-center justify-center px-2 sm:w-10 sm:h-10 w-8 h-8" role="button" tabindex="0" aria-label="Increase Quantity">
                                            <img src="http://127.0.0.1:8000/widian-assets/images/plus.svg" alt="plus" class="max-w-3 h-3">
                                        </span>
                                    </div>
                                    
                                    
                                </div>
                            </div>
                            
                        </div>    
                        <div class="flex-1">
                            <h2 class="text-xl font-bold text-black mb-5">Why are you returning this? (required)</h2>
                            <div class="border px-5 rounded-md border-[#EFECE2]">
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Inaccurate website description</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">No longer needed</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Item arrived too late</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Didn’t approve purchase</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Missing or broken parts</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Product and shipping box both damaged</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Product doesn’t meet expectations</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Product damaged, but shipping box OK</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Received extra item (no refund needed)</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Wrong item was sent</p>
                                    </div>
                                </label>
                                <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                    <div class="relative w-5 h-5">
                                        <input
                                            type="radio"
                                            name="shipping_method"
                                            value="1"
                                            class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                        />
                                        <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                    </div>
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray">Other</p>
                                    </div>
                                </label>

                                <div class="py-5 space-y-1">
                                    <label class="text-sm text-dark-gray-2" for="">Comments (required)</label>
                                    <textarea name="order_comments" class="text-base text-dark-gray-3 resize-none h-[114px] p-3 border border-light-gray text-dark-gray-3 outline-none w-full rounded-md"></textarea>
                                </div>
                            </div>
                            <button @click="goToNext" class="w-full mt-5 capitalize text-base bg-secondary hover:bg-black duration-300 text-white py-3 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed">
                                Continue
                            </button>
                        </div>           
                    </div>

                    <div v-if="step == 2" class="flex xl:flex-nowrap flex-wrap xl:gap-[72px] gap-10">
                        <div class="mb-6 flex-1"> 
                            <!-- Product 1 -->
                            <div class="flex items-center gap-4 border-b border-gray-200 pb-8">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                            </div>
                            
                        </div>    
                        <div class="flex-1">
                            <div>
                                <h2 class="text-xl font-bold text-black mb-5">Why are you returning this? (required)</h2>
                                <div class="border p-5 rounded-md border-[#EFECE2]">
                                    <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                        <p class="text-base leading-snug text-dark-gray font-semibold">Product and shipping box both damaged</p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-black my-5">How can we make it right?</h2>
                                <div class="border px-5 rounded-md border-[#EFECE2]">
                                    <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5">
                                        <div class="relative w-5 h-5">
                                            <input
                                                type="radio"
                                                name="shipping_method"
                                                value="1"
                                                class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                            />
                                            <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                        </div>
                                        <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                            <p class="text-base leading-snug text-dark-gray">Refund to original payment method</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            <div class="mt-5">
                                <div class="border px-5 rounded-md border-[#EFECE2]">
                                    <label class="flex gap-2 md:gap-[15px] items-start cursor-pointer py-5 border-b border-[#EFECE2]">
                                        <div class="relative w-5 h-5">
                                            <input
                                                type="radio"
                                                name="shipping_method"
                                                value="1"
                                                class="peer absolute inset-0 w-full h-full appearance-none rounded-full border-2 border-secondary checked:bg-transparent cursor-pointer"
                                            />
                                            <span class="pointer-events-none absolute top-1/2 left-1/2 h-2 w-2 rounded-full bg-secondary opacity-0 peer-checked:opacity-100 transform -translate-x-1/2 -translate-y-1/2"></span>
                                        </div>
                                        <div class="text-xs md:text-sm lg:text-base text-dark-gray">
                                            <p class="text-base leading-snug text-dark-gray">Pickup by FedEx</p>
                                        </div>
                                    </label>
                                    <p class="text-base text-dark-gray-2 my-5">Your package will be picked up by courier service. Please keep your return item(s) ready.</p>
                                    <div>
                                        <p class="text-base font-bold text-dark-gray-2 pb-4">Pickup Address</p>
                                        <p class="text-dark-gray-2 text-base pb-4">Office 210, 2nd Floor, 
                                        Business Bay Tower A, Business Bay, 
                                        Dubai, United Arab Emirates</p>
                                        <button class="w-fit mb-5 capitalize text-sm bg-secondary hover:bg-black duration-300 text-white py-2 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed">
                                            <span> Change Address </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button @click="goToNext" class="w-full mt-5 capitalize text-base bg-secondary hover:bg-black duration-300 text-white py-3 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed">
                                Confirm Your Return
                            </button>
                        </div>           
                    </div>

                    <div v-if="step == 3">
                        <div class=" text-center space-y-2 mb-6">
                            <p class="text-[#3D3C3A] font-bold text-base">Return/Refund Status</p>
                            <p class="text-[#707070] text-sm">05 June, 2025 - Initiated Return/Refund</p>
                        </div>

                        <ul class="flex items-center justify-between w-full mb-[30px]">
                            <li class="flex flex-col items-center relative w-1/5 after:content-[''] after:w-full after:h-1 after:bg-secondary after:inline-block after:absolute after:top-[12px] after:left-1/2 after:z-0 last:after:hidden">
                                <div class="z-10">
                                    <span class="bg-secondary w-[26px] h-[26px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                        <img src="{{ asset('widian-assets/images/tick.svg') }}" class="w-2.5" alt="Completed">
                                    </span>
                                </div>
                                <div class="mt-4 text-center">
                                    <p class="text-sm font-medium">Initiated</p>
                                    <p class="text-sm text-dark-gray-2">05 June, 2025</p>
                                </div>
                            </li>
                            <li class="flex flex-col items-center relative w-1/5 after:content-[''] after:w-full after:h-1 after:bg-secondary after:inline-block after:absolute after:top-[12px] after:left-1/2 after:z-0 last:after:hidden">
                                <div class="z-10">
                                    <span class="bg-secondary w-[26px] h-[26px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                        <img src="{{ asset('widian-assets/images/tick.svg') }}" class="w-2.5" alt="Completed">
                                    </span>
                                </div>
                                <div class="mt-4 text-center">
                                    <p class="text-sm font-medium">Processing</p>
                                    <p class="text-sm text-dark-gray-2">07 June, 2025</p>
                                </div>
                            </li>
                            <li class="flex flex-col items-center relative w-1/5 after:content-[''] after:w-full after:h-1 after:bg-[#CCC] after:inline-block after:absolute after:top-[12px] after:left-1/2 after:z-0 last:after:hidden">
                                <div class="z-10">
                                    <span class="bg-secondary w-[26px] h-[26px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                        <img src="{{ asset('widian-assets/images/tick.svg') }}" class="w-2.5" alt="Completed">
                                    </span>
                                </div>
                                <div class="mt-4 text-center">
                                    <p class="text-sm font-medium">Pickup Scheduled</p>
                                    <p class="text-sm text-dark-gray-2">10 June, 2025</p>
                                </div>
                            </li>
                            <li class="flex flex-col items-center relative w-1/5 after:content-[''] after:w-full after:h-1 after:bg-[#CCC] after:inline-block after:absolute after:top-[12px] after:left-1/2 after:z-0 last:after:hidden">
                                <div class="z-10">
                                    <span class="bg-[#CCC] w-[26px] h-[26px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                        <span class="text-xs">4</span>
                                    </span>
                                </div>
                                <div class="mt-4 text-center">
                                    <p class="text-sm font-medium">Item Received</p>
                                    <p class="text-sm text-dark-gray-2">Pending</p>
                                </div>
                            </li>
                            <li class="flex flex-col items-center relative w-1/5">
                                <div class="z-10">
                                    <span class="bg-[#CCC] w-[26px] h-[26px] border-2 border-transparent rounded-full flex justify-center items-center mx-auto text-sm text-white">
                                        <span class="text-xs">5</span>
                                    </span>
                                </div>
                                <div class="mt-4 text-center">
                                    <p class="text-sm font-medium">Refund Processed</p>
                                    <p class="text-sm text-dark-gray-2">Pending</p>
                                </div>
                            </li>
                        </ul>

                        <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                            <div class="flex items-center gap-4 flex-1">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                            </div>
                            <div class="flex-1 space-y-1">
                                <p class="text-[#3D3C3A] font-bold text-sm">Return Initiated</p>
                                <p class="text-[#707070] text-sm">18 June, 2025</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                            <div class="flex items-center gap-4 flex-1">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                            </div>
                            <div class="flex-1 space-y-1">
                                <p class="text-[#3D3C3A] font-bold text-sm">Return Initiated</p>
                                <p class="text-[#707070] text-sm">18 June, 2025</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 border-b border-gray-200 py-8">
                            <div class="flex items-center gap-4 flex-1">
                                <div class="h-[100px] w-[100px] overflow-hidden rounded">
                                    <img 
                                        class="h-full w-full object-cover" 
                                        src="{{ asset('storage/product/36/B2qBl6eOXXOWvjpfqAFOeIjZ0wRmtUH3n4L3LlA8.webp') }}" 
                                        alt="SAHARA Gold Collection"
                                    />
                                </div>
                                
                                <div class="flex-1 space-y-2.5">
                                    <p class="text-xl font-semibold text-secondary max-md:text-xl max-sm:text-base leading-[16px]">SAHARA</p>
                                    <p class="text-xs text-dark-gray-2 ">Gold Collection</p>
                                    <p class="text-xs text-dark-gray-2">x 2</p>
                                </div>
                            </div>
                            <div class="flex-1 space-y-1">
                                <p class="text-[#3D3C3A] font-bold text-sm">Return Initiated</p>
                                <p class="text-[#707070] text-sm">18 June, 2025</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </script>
        <script type="module">
            app.component('return-refunds', {
                template: '#v-return-refunds-template',

                data() {
                    return {
                        step: 1
                    };
                },

                methods: {
                    goToNext() {
                        this.step++;
                        window.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        </script>
    @endPushOnce
</x-shop::layouts.account>
