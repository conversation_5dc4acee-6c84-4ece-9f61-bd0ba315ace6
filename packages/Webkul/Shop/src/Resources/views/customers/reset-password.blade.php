<!-- SEO Meta Content -->
@push('meta')
    <meta
        name="description"
        content="@lang('shop::app.customers.reset-password.title')"
    />

    <meta
        name="keywords"
        content="@lang('shop::app.customers.reset-password.title')"
    />
@endPush

<x-shop::layouts
    :has-header="true"
    :has-feature="false"
    :has-footer="true"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.reset-password.title')
    </x-slot>

    <section class="grid grid-cols-1 lg:grid-cols-2 items-center ">
        <div class="h-full">
            <img src="{{ asset('widian-assets/images/register.jpg') }}" class="w-full h-full object-cover" alt="">
        </div>

        <div class="px-5 lg:px-10 xl:px-20 2xl:px-36 max-2xl:py-10">
            <h1 class="text-2xl 2xl:text-4xl leading-[42px] font-medium font-playfair text-secondary">
                @lang('shop::app.customers.reset-password.title')
            </h1>

            <x-shop::form :action="route('shop.customers.reset_password.store', app()->getLocale())" class="py-7">
                <x-shop::form.control-group.control
                    type="hidden"
                    name="token"
                    :value="$token"
                />

                {!! view_render_event('bagisto.shop.customers.reset_password_form_controls.before') !!}

                <!-- Email -->
                <x-shop::form.control-group>
                    <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                        @lang('shop::app.customers.reset-password.email')
                    </x-shop::form.control-group.label>

                    <x-shop::form.control-group.control
                        type="email"
                        class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                        id="email"
                        name="email"
                        rules="required|email"
                        :value="old('email')"
                        :label="trans('shop::app.customers.reset-password.email')"
                        placeholder="<EMAIL>"
                        :aria-label="trans('shop::app.customers.reset-password.email')"
                        aria-required="true"
                    />

                    <x-shop::form.control-group.error control-name="email" />
                </x-shop::form.control-group>

                <!-- Password -->
                <x-shop::form.control-group>
                    <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                        @lang('shop::app.customers.reset-password.password')
                    </x-shop::form.control-group.label>

                    <x-shop::form.control-group.control
                        type="password"
                        class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                        name="password"
                        rules="required|min:6"
                        value=""
                        :label="trans('shop::app.customers.reset-password.password')"
                        :placeholder="trans('shop::app.customers.reset-password.password')"
                        ref="password"
                        :aria-label="trans('shop::app.customers.reset-password.password')"
                        aria-required="true"
                    />

                    <x-shop::form.control-group.error control-name="password" />
                </x-shop::form.control-group>

                <!-- Confirm Password -->
                <x-shop::form.control-group>
                    <x-shop::form.control-group.label class="text-black leading-[22px] inline-block !mb-0">
                        @lang('shop::app.customers.reset-password.confirm-password')
                    </x-shop::form.control-group.label>

                    <x-shop::form.control-group.control
                        type="password"
                        class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                        name="password_confirmation"
                        rules="confirmed:@password"
                        value=""
                        :label="trans('shop::app.customers.reset-password.confirm-password')"
                        :placeholder="trans('shop::app.customers.reset-password.confirm-password')"
                        :aria-label="trans('shop::app.customers.reset-password.confirm-password')"
                        aria-required="true"
                    />

                    <x-shop::form.control-group.error control-name="password" />
                </x-shop::form.control-group>

                {!! view_render_event('bagisto.shop.customers.reset_password_form_controls.after') !!}

                {!! view_render_event('bagisto.shop.customers.reset_password.submit_button.before') !!}

                <!-- Submit Button -->
                <button type="submit" class="bg-secondary hover:bg-black duration-300 min-w-[150px] sm:min-w-[300px] py-3 sm:py-5 text-base md:text-xl leading-6 rounded-[8px] md:rounded-[10px] text-white ltr:ml-0 rtl:mr-0">
                    @lang('shop::app.customers.reset-password.submit-btn-title')
                </button>

                {!! view_render_event('bagisto.shop.customers.reset_password.submit_button.after') !!}
            </x-shop::form>
        </div>
    </section>
</x-shop::layouts>
