<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="@lang('shop::app.customers.forgot-password.title')"/>

    <meta name="keywords" content="@lang('shop::app.customers.forgot-password.title')"/>
@endPush

<x-shop::layouts
    :has-header="true"
    :has-feature="false"
    :has-footer="true"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.forgot-password.title')
    </x-slot>

    <section class="grid grid-cols-1 lg:grid-cols-2 items-center ">
        <div class="h-full">
            <img src="{{ asset('widian-assets/images/register.jpg') }}" class="w-full h-full object-cover" alt="">
        </div>

        <div class="px-5 lg:px-10 xl:px-20 2xl:px-36 max-2xl:py-10">
            <h6 class="font-playfair uppercase max-md:text-md text-lg text-black leading-6 font-medium mb-4">@lang('shop::app.customers.forgot-password.forgot-password')</h6>

            <h1 class="text-2xl 2xl:text-4xl leading-[42px] font-medium font-playfair text-secondary">
                @lang('shop::app.customers.forgot-password.title')
            </h1>


            <x-shop::form :action="route('shop.customers.forgot_password.store', app()->getLocale())" class="py-[50px]">
                <div class="flex gap-2 md:gap-4 items-center pt-0 pb-5">
                    <span class="text-xs md:text-sm lg:text-base text-dark-gray">
                        @lang('shop::app.customers.forgot-password.forgot-password-text')
                    </span>
                </div>

                {!! view_render_event('bagisto.shop.customers.forget_password_form_controls.before') !!}

                <x-shop::form.control-group>
                    <x-shop::form.control-group.label class="text-black leading-[22px] inline-block">
                        @lang('shop::app.customers.login-form.email')
                    </x-shop::form.control-group.label>

                    <x-shop::form.control-group.control
                        type="email"
                        class="w-full h-[56px] outline-none placeholder:text-light-gray leading-[22px] text-dark-gray border-b border-light-gray"
                        name="email"
                        rules="required|email"
                        value=""
                        :label="trans('shop::app.customers.login-form.email')"
                        :placeholder="trans('shop::app.customers.forgot-password.email-address')"
                        :aria-label="trans('shop::app.customers.login-form.email')"
                        aria-required="true"
                    />

                    <x-shop::form.control-group.error control-name="email" />
                </x-shop::form.control-group>

                {!! view_render_event('bagisto.shop.customers.forget_password_form_controls.email.after') !!}

                <!-- Submit Button -->
                <button type="submit" class="bg-secondary hover:bg-black duration-300 min-w-[150px] sm:min-w-[300px] py-3 sm:py-5 text-base md:text-xl leading-6 rounded-[8px] md:rounded-[10px] text-white ltr:ml-0 rtl:mr-0">
                    @lang('shop::app.customers.forgot-password.submit')
                </button>

                {!! view_render_event('bagisto.shop.customers.forget_password_form_controls.after') !!}

            </x-shop::form>

            <p class="flex items-center text-base text-dark-gray leading-[22px]">
                <a href="{{ route('shop.customer.session.index', app()->getLocale()) }}" class="ps-1 text-dark-gray hover:text-secondary transition duration-500">
                    @lang('shop::app.customers.forgot-password.back')
                </a>
            </p>
        </div>
    </section>
</x-shop::layouts>
