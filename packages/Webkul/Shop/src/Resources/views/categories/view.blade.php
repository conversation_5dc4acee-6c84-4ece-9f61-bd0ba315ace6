<!-- SEO Meta Content -->
@push('meta')
    <meta
        name="description"
        content="{{ trim($category->meta_description) != "" ? $category->meta_description : \Illuminate\Support\Str::limit(strip_tags($category->description), 120, '') }}"
    />

    <meta
        name="keywords"
        content="{{ $category->meta_keywords }}"
    />

    @if (core()->getConfigData('catalog.rich_snippets.categories.enable'))
        <script type="application/ld+json">
            {!! app('Webkul\Product\Helpers\SEO')->getCategoryJsonLd($category) !!}
        </script>
    @endif
@endPush

<x-shop::layouts>
    <!-- Page Title -->
    <x-slot:title>
        {{ trim($category->meta_title) != "" ? $category->meta_title : $category->name }}
    </x-slot>

    <!-- Banner -->

    @if ($category->banner_image)
        {!! view_render_event('bagisto.shop.categories.view.banner_image.before') !!}

        <section class="bg-primary">
            <img class="w-full" src="{{ $category->banner_image_url }}" alt="{{ $category->name }}" data-aos="fade" data-aos-duration="1000" data-aos-easing="ease"/>
        </section>

        {!! view_render_event('bagisto.shop.categories.view.banner_image.after') !!}
    @endif

    <!-- Collection -->
    <section class="py-20 bg-primary max-xl:px-5">
        <div class="max-w-[1100px] mx-auto space-y-10 text-center">
            <h3 class="text-black text-xl md:text-2xl font-normal font-playfair uppercase leading-7" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-in">
                {{ $category->header ?? '' }}
            </h3>

            <h1 class="text-secondary text-3xl md:text-4xl xl:text-5xl font-medium font-playfair leading-[60px]" data-aos="zoom-in" data-aos-duration="600" data-aos-easing="ease-out">
                {{ $category->name }}
            </h1>

            {!! view_render_event('bagisto.shop.categories.view.description.before') !!}

            <p class="text-dark-gray-2 md:text-lg xl:text-2xl font-normal" data-aos="fade-up" data-aos-duration="600" data-aos-easing="ease-out">
                {{ strip_tags($category->description) }}
            </p>

            {!! view_render_event('bagisto.shop.categories.view.description.after') !!}
        </div>
    </section>

    <!-- Collection Products -->
    <section class="bg-primary-dark">
        <div class="product-grid product-grid-3 grid grid-cols-2 md:grid-cols-3 max-w-[1920px] mx-auto">
            @forelse ($category->products as $product)
                <div class="p-5 text-center bg-primary product-card relative group">
                    <a href="{{ route('shop.product.index', [app()->getLocale(), $product->url_key]) }}">
                        <img src="{{ product_image()->getProductBaseImage($product)['original_image_url'] }}" alt="{{ $product->name }}" class="mx-auto w-full lg:h-[400px] md:h-[300px] h-[280px] object-cover aos-init aos-animate" data-aos="zoom-out" data-aos-duration="800" data-aos-easing="ease-out" />
                    </a>

                    <div class="relative pb-10 lg:pb-[70px]">
                        <div class="transition-all ease-in-out lg:-bottom-3 duration-500 relative lg:-translate-x-1/2 lg:left-1/2 lg:group-hover:bottom-3 max-lg:mb-5">
                            <a href="{{ route('shop.product.index', [app()->getLocale(), $product->url_key]) }}" class="text-center text-black text-xl md:text-2xl font-normal font-playfair uppercase leading-loose tracking-wider mb-5 pt-5" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-in">
                                {{ $product->name }}
                            </a>

                            <div class="flex items-center justify-center gap-3 flex-wrap sm:mb-0 mb-10">
                                <p class="text-center text-sm md:text-base text-[#3D3C3A] font-normal leading-snug tracking-wide" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-in" data-aos-delay="100">
                                    {{ core()->formatPrice(core()->convertPrice($product->special_price ?? $product->price, core()->getCurrentCurrencyCode(), core()->getCurrentCurrencyCode()), core()->getCurrentCurrencyCode()) }}
                                </p>
                                @if($product->special_price && $product->special_price != $product->price)
                                    <p class="line-through text-center text-xs md:text-sm opacity-50 text-[#3D3C3A] font-normal leading-snug tracking-wide" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-in" data-aos-delay="150">
                                        {{ core()->formatPrice(core()->convertPrice($product->price, core()->getCurrentCurrencyCode(), core()->getCurrentCurrencyCode()), core()->getCurrentCurrencyCode()) }}
                                    </p>
                                @endif
                            </div>
                        </div>

                        <a href="{{ route('shop.product.index', [app()->getLocale(), $product->url_key]) }}" class="absolute bottom-4 inset-x-0 m-auto w-fit
                            opacity-0 invisible
                            group-hover:visible group-hover:opacity-100
                            px-4 2xl:px-6 py-2 border border-black uppercase text-xs sm:text-sm md:text-md 2xl:text-lg rounded-full
                            hover:bg-secondary hover:text-white hover:border-secondary
                            transition-all duration-500 ease-in-out">
                            @lang('shop::app.categories.discover-more')
                        </a>
                    </div>
                </div>
            @empty
                <div class="col-span-full text-center py-10">
                    <p>No products found in this category.</p>
                </div>
            @endforelse
        </div>
    </section>

    <!-- You May Also Like -->
    <you-may-also-like-products />

    @pushOnce('scripts')
        <script type="text/x-template" id="v-you-may-also-like-products-template">
            <template v-if="isLoading">
                <x-shop::shimmer.products.cards.grid count="4" />
            </template>
            <template v-else-if="products.length > 0">
                <section class="bg-primary pt-[120px]">
                    <h1 class="text-secondary text-center text-3xl md:text-4xl xl:text-5xl font-medium font-playfair leading-[50px] mb-[60px]" data-aos="zoom-in" data-aos-duration="600" data-aos-easing="ease-out">
                        @lang('shop::app.categories.you-may-also-like')
                    </h1>

                    <div class="product-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 max-w-[1920px] mx-auto">
                        <div v-for="product in products" :key="product.id" class="p-5 text-center bg-primary product-card relative group">
                            <span v-if="product.is_new" class="absolute w-12 h-6 flex items-center justify-center pt-[3px] top-5 left-5 z-10 bg-secondary text-white text-xs font-bold rounded-full">
                                @lang('shop::app.categories.new')
                            </span>

                            <ul class="absolute right-5 top-5 space-y-3 hidden group-hover:block transition ease-in-out duration-200 z-10">
                                <button @click="quickViewProduct(product)" class="w-10 h-10 bg-secondary rounded-full flex justify-center items-center cursor-pointer">
                                    <img src="{{ asset('widian-assets/images/quick-view.svg') }}" alt="quick-view" />
                                </button>

                                @if (core()->getConfigData('sales.checkout.shopping_cart.cart_page'))
                                    {!! view_render_event('bagisto.shop.components.products.card.add_to_cart.before') !!}

                                    <button
                                        class="w-10 h-10 bg-secondary rounded-full flex justify-center items-center cursor-pointer"
                                        :disabled="!product.is_saleable || isAddingToCart"
                                        @click="addToCartFromHome(product)"
                                    >
                                        <img src="{{ asset('widian-assets/images/cart-icon.svg') }}" alt="cart" />
                                    </button>

                                    {!! view_render_event('bagisto.shop.components.products.card.add_to_cart.after') !!}
                                @endif
                            </ul>

                            <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)">
                                <div class="h-[400px] sm:h-[459px] relative w-full overflow-hidden mt-0 aos-init aos-animate" data-aos="zoom-out" data-aos-duration="800" data-aos-easing="ease-out">
                                    <img
                                    :src="product.cover_images[0]?.url ?? product.base_image.original_image_url"
                                    :alt="product.name"
                                    class="absolute inset-0 w-full h-full object-contain sm:object-cover transition-opacity duration-300 ease-in-out opacity-100 group-hover:opacity-0"
                                    />

                                    <img
                                    :src="product.hover_images[0]?.url ?? product.base_image.original_image_url"
                                    :alt="product.name"
                                    class="absolute inset-0 w-full h-full object-contain sm:object-cover transition-opacity duration-300 ease-in-out opacity-0 group-hover:opacity-100"
                                  />
                              </div>
                            </a>

                            <a :href="'{{ route('shop.product.index', [app()->getLocale(), ':url_key']) }}'.replace(':url_key', product.url_key)" class="text-center text-black text-xl md:text-2xl font-normal font-playfair uppercase leading-loose tracking-wider" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="200">
                                @{{ product.name }}
                            </a>

                            <div class="flex items-center justify-center gap-3 flex-wrap" data-aos="fade" data-aos-duration="800" data-aos-easing="ease-out" data-aos-delay="300">
                                <p class="text-center text-sm md:text-base text-[#3D3C3A] font-normal leading-snug tracking-wide">
                                    @{{ product.prices.final.formatted_price }}
                                </p>
                                <p v-if="product.prices.regular.formatted_price != product.prices.final.formatted_price"
                                    class="line-through text-center text-xs md:text-sm opacity-50 text-[#3D3C3A] font-normal leading-snug tracking-wide">
                                    @{{ product.prices.regular.formatted_price }}
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
                <x-shop::form v-slot="{ meta, errors, handleSubmit }" as="div">
                    <form ref="formData" @submit="handleSubmit($event, addToCart)">
                        <x-shop::modal ref="quickViewModal">
                            <x-slot:header class="!justify-end !p-4"></x-slot:header>
                            <x-slot:content class="!px-4">
                                <div v-if="currentQuickViewProduct" class="flex flex-col sm:flex-row gap-3 sm:gap-5 items-start mb-5">
                                    <x-shop::form.control-group.control type="hidden" name="product_id" ::value="currentQuickViewProduct.id" />
                                    <div class="w-full sm:w-1/3 h-52">
                                        <img :src="currentQuickViewProduct.base_image.original_image_url" :alt="currentQuickViewProduct.name" class="mx-auto w-full h-full object-contain" />
                                    </div>

                                    <div class="w-full sm:w-2/3 text-center sm:text-start">
                                        <div class="flex items-center gap-3 mb-3">
                                            <h1 class="text-secondary text-xl xl:text-2xl font-medium font-playfair">@{{ currentQuickViewProduct.name }}</h1>
                                            <p v-if="currentQuickViewProduct.category" class="text-black text-md xl:text-lg font-normal mt-2">@{{ currentQuickViewProduct.category?.name }}</p>
                                        </div>
                                        <div class="flex items-center justify-center sm:justify-start gap-5 pb-2">
                                            <span class="text-black text-md xl:text-lg font-normal capitalize">@{{ currentQuickViewProduct.prices.final.formatted_price }}</span>
                                            <span v-if="currentQuickViewProduct.prices.regular.formatted_price != currentQuickViewProduct.prices.final.formatted_price" class="opacity-50 text-black text-lg xl:text-base font-normal line-through capitalize">@{{ currentQuickViewProduct.prices.regular.formatted_price }}</span>
                                        </div>

                                        <div v-if="currentQuickViewProduct.product_size && currentQuickViewProduct.product_size.admin_name" class="mt-2.5">
                                            <div class="flex flex-wrap gap-5">
                                                <div class="text-black text-sm font-normal p-2 border border-black">@{{ currentQuickViewProduct.product_size.admin_name }} @lang('shop::app.products.view.extrait-de-parfum')</div>
                                            </div>
                                        </div>
                                        <x-shop::quantity-changer ::max="currentQuickViewProduct.stock_qty" name="quantity" ::value="1" class="mt-5 inline-flex border border-light-gray"/>

                                    </div>
                                </div>
                                <div class="border-light-gray border-t pt-5">
                                    <button type="submit" class="bg-black hover:bg-secondary duration-300 flex items-center justify-center w-full text-white text-lg font-bold uppercase leading-normal py-[18px] px-3 rounded-[10px] h-12">
                                    <span class="mt-1">@lang('shop::app.products.view.add-to-cart')</span>
                                    </button>
                                </div>
                            </x-slot>
                        </x-shop::modal>
                    </form>
                </x-shop::form>
            </template>
        </script>

        <script type="module">
            app.component('you-may-also-like-products', {
                template: '#v-you-may-also-like-products-template',

                data() {
                    return {
                        isLoading: true,
                        products: [],
                        isAddingToCart: false,
                        currentQuickViewProduct: null,
                    }
                },

                mounted() {
                    this.getProducts();

                    this.$nextTick(() => {
                        setTimeout(() => {
                            if (window.Aos) {
                                window.Aos.refresh();
                            }
                            if (window.refreshAOS) {
                                window.refreshAOS();
                            }
                        }, 200);
                    });
                },

                updated() {
                    this.$nextTick(() => {
                        if (window.Aos) {
                            window.Aos.refresh();
                        }

                        if (window.refreshAOS) {
                            window.refreshAOS();
                        }
                    });
                },

                methods: {
                    getProducts() {
                        this.$axios.get(`{{ route('shop.api.categories.you-may-also-like-products', ['locale' => app()->getLocale(), 'category_id' => $category->id]) }}`)
                            .then(response => {
                                this.isLoading = false;
                                this.products = response.data.data;

                                this.$nextTick(() => {
                                    setTimeout(() => {
                                        if (window.Aos) {
                                            window.Aos.refresh();
                                        }

                                        if (window.refreshAOS) {
                                            window.refreshAOS();
                                        }
                                    }, 100);
                                });
                            }).catch(error => {
                                this.isLoading = false;
                            });
                    },

                    addToCart(params) {
                        this.isAddingToCart = true;

                        this.$axios.post('{{ route("shop.api.checkout.cart.store", app()->getLocale()) }}', params)
                        .then(response => {
                            if (response.data.message) {
                                this.$emitter.emit('update-mini-cart', response.data.data );

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                            }

                            this.isAddingToCart = false;
                        })
                        .catch(error => {
                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                            if (error.response.data.redirect_uri) {
                                window.location.href = error.response.data.redirect_uri;
                            }

                            this.isAddingToCart = false;
                        });
                    },

                    addToCartFromHome(product) {
                        const params = {
                            'quantity': 1,
                            'product_id': product.id,
                        };

                        this.addToCart(params);
                    },

                    addToCartFromQuickView(data) {
                        const params = {
                            quantity: this.$refs.formData.quantity.value,
                            product_id: this.$refs.formData.product_id.value,
                        };

                        this.addToCart(params);
                        this.$refs.formData.reset();
                        this.$refs.quickViewModal.close();
                    },

                    quickViewProduct(product) {
                        this.currentQuickViewProduct = product;
                        this.$refs.quickViewModal.open();
                    },
                },
            });
        </script>
    @endPushOnce
</x-shop::layouts>
