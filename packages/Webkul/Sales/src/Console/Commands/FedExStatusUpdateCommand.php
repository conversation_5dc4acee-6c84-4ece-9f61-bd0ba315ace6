<?php

namespace Webkul\Sales\Console\Commands;

use Illuminate\Console\Command;
use Webkul\Sales\Models\Order;
use Webkul\Sales\Models\OrderNote;
use Webkul\Sales\Services\FedExTrackingService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FedExStatusUpdateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fedex:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update order statuses based on FedEx tracking information';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Starting FedEx status update...');

        // Get orders that are ready for shipment or in shipment
        $orders = Order::whereNotIn('status', [Order::STATUS_COMPLETED, Order::STATUS_CANCELED, Order::STATUS_CLOSED, Order::STATUS_FRAUD])->get();

        if(isset($orders) && count($orders) > 0 && $orders->isNotEmpty()) {
            foreach ($orders as $order) {

                if ($order->canShip()) {
                    continue;
                }

                try {
                    $orderNote = $order->order_notes()
                        ->latest()
                        ->first();
    
                    if (!$orderNote) {
                        continue;
                    }
    
                    $fedExService = new FedExTrackingService();
    
                    if ($orderNote->tracking_number) {
                        $trackingInfo = $fedExService->getTrackingStatus($orderNote->tracking_number, $order);
                    } else {
                        continue;
                    }
    
                    if (!$trackingInfo) {
                        continue;
                    }
    
                    $status = $trackingInfo['status'];
                    $lastStatus = $orderNote->status;
    
                    if ($lastStatus != $status) {
                        $order->update(['status' => $status]);
                        
                        OrderNote::create([
                            'order_id' => $order->id,
                            'customer_id' => $order->customer_id,
                            'status' => $status,
                        ]);
                    }
    
                    if (strtolower($status) === 'delivered') {
                        $deliveredNote = OrderNote::where('order_id', $order->id)
                            ->where('status', 'Delivered')
                            ->orderBy('created_at', 'asc')
                            ->first();
    
                        if ($deliveredNote) {
                            $deliveryTime = $deliveredNote->created_at;
                            $now = Carbon::now();
                            
                            if ($now->diffInHours($deliveryTime) >= 24) {
                                $order->update(['status' => Order::STATUS_COMPLETED]);
    
                                OrderNote::create([
                                    'order_id' => $order->id,
                                    'customer_id' => $order->customer_id,
                                    'status' => Order::STATUS_COMPLETED
                                ]);
                            }
                        }
                    }
    
                    Log::info("Updated status for order #{$order->id} to {$status}");
    
                } catch (\Exception $e) {
                    Log::error("Error updating order #{$order->id}: " . $e->getMessage());
                }
            }
    
            Log::info('FedEx status update completed.');
        }

    }
}