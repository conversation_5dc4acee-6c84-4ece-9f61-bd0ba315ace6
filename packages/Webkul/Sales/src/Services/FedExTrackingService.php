<?php

namespace Webkul\Sales\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Webkul\Admin\Models\HealthCheckLog;
use Webkul\Admin\Repositories\HealthCheckLogRepository;
use Illuminate\Support\Facades\Cache;

class FedExTrackingService
{
    public $client;
    protected $apiEndpoint;
    protected $shipClientId;
    protected $shipClientSecret;
    protected $accountNumber;
    protected $trackClientId;
    protected $trackClientSecret;
    protected $healthCheckLogRepository;

    /**
     * Create a new FedEx tracking service instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->client = new Client();

        $this->healthCheckLogRepository = app(HealthCheckLogRepository::class);

        $this->apiEndpoint = core()->getConfigData('sales.carriers.fedex.base_url');
        $this->shipClientId = core()->getConfigData('sales.carriers.fedex.ship_api_key');
        $this->shipClientSecret = core()->getConfigData('sales.carriers.fedex.ship_api_secret');
        $this->accountNumber = core()->getConfigData('sales.carriers.fedex.account_number');
        $this->trackClientId = core()->getConfigData('sales.carriers.fedex.track_api_key');
        $this->trackClientSecret = core()->getConfigData('sales.carriers.fedex.track_api_secret');
    }

    /**
     * Generate a tracking number for a shipment.
     *
     * @param  array  $shipmentData
     * @return array
     */
    public function getTrackingNumber(array $shipmentData)
    {
        try {
            $authToken = $this->getAuthToken($this->shipClientId, $this->shipClientSecret);

            $shipmentRequest = [
                'accountNumber' => [
                    'value' => $this->accountNumber
                ],
                'labelResponseOptions' => 'URL_ONLY',
                'requestedShipment' => [
                    'shipper' => $this->getOriginAddress(),
                    'recipients' => [
                        $this->getDestinationAddress($shipmentData)
                    ],
                    'pickupType' => 'USE_SCHEDULED_PICKUP',
                    'serviceType' => 'INTERNATIONAL_ECONOMY',
                    'packagingType' => 'YOUR_PACKAGING',
                    'shippingChargesPayment' => [
                        'paymentType' => 'SENDER',
                        'payor' => [
                            'responsibleParty' => [
                                'accountNumber' => [
                                    'value' => $this->accountNumber
                                ]
                            ]
                        ]
                    ],
                    'labelSpecification' => [
                        'imageType' => 'PNG',
                        'labelStockType' => 'PAPER_4X6'
                    ],
                    'customsClearanceDetail' => [
                        'dutiesPayment' => [
                            'paymentType' => 'SENDER',
                            'payor' => [
                                'responsibleParty' => [
                                    'accountNumber' => [
                                        'value' => $this->accountNumber
                                    ]
                                ]
                            ]
                        ],
                        'commodities' => [
                            [
                                'description' => $shipmentData['description'],
                                'quantity' => $shipmentData['quantity'],
                                'quantityUnits' => 'PCS',
                                'customsValue' => [
                                    'amount' => core()->convertPrice($shipmentData['value'], 'USD'),
                                    'currency' => 'USD'
                                ],
                                'weight' => [
                                    'units' => 'KG',
                                    'value' => (float) $shipmentData['weight']
                                ],
                                'countryOfManufacture' => 'AE',
                                'unitPrice' => [
                                    'amount' => core()->convertPrice($shipmentData['value'], 'USD'),
                                    'currency' => 'USD'
                                ],
                                'numberOfPieces' => $shipmentData['quantity']
                            ]
                        ]
                    ],
                    'requestedPackageLineItems' => [
                        [
                            'weight' => [
                                'units' => 'KG',
                                'value' => (float) $shipmentData['weight']
                            ],
                            'dimensions' => [
                                'length' => (float) $shipmentData['length'],
                                'width' => (float) $shipmentData['width'],
                                'height' => (float) $shipmentData['height'],
                                'units' => 'CM'
                            ]
                        ]
                    ]
                ]
            ];
            
            $response = $this->client->post($this->apiEndpoint . 'ship/v1/shipments', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $authToken,
                    'Content-Type' => 'application/json'
                ],
                'json' => $shipmentRequest
            ]);

            $status = $response->getStatusCode() === 200 ? HealthCheckLog::STATUS_SUCCESS : HealthCheckLog::STATUS_FAILURE;

            $healthCheckLogData = [
                'success' => $response->getStatusCode() === 200 ? true : false,
                'message' => $response->getStatusCode() === 200 ? 'Successfully generated tracking number' : 'Failed to generate tracking number',
                'timestamp' => now()->toDateTimeString(),
            ];

            $this->createFedexLog($this->apiEndpoint . 'ship/v1/shipments', $status, $healthCheckLogData);

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);

                $trackingInfo = [
                    'tracking_number' => $data['output']['transactionShipments'][0]['masterTrackingNumber'],
                    'label_url' => $data['output']['transactionShipments'][0]['pieceResponses'][0]['packageDocuments'][0]['url'],
                ];

                return $trackingInfo;
            }

            throw new \Exception('Failed to generate tracking number');
        } catch (\Exception $e) {
            $healthCheckLogData = [
                'success' => false,
                'message' => 'Failed to generate tracking number',
                'timestamp' => now()->toDateTimeString(),
            ];

            $this->createFedexLog($this->apiEndpoint . 'ship/v1/shipments', HealthCheckLog::STATUS_FAILURE, $healthCheckLogData, $e->getMessage());

            throw new \Exception('Error generating tracking number');
        }
    }

    /**
     * Get authentication token from FedEx.
     *
     * @return string
     */
    protected function getAuthToken($clientId, $clientSecret)
    {
        try {
            $token = Cache::get('fedex_auth_token' . $clientId . $clientSecret);
            
            if ($token) {
                return $token;
            }

            $response = $this->client->post($this->apiEndpoint . 'oauth/token', [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret
                ]
            ]);

            $data = json_decode($response->getBody(), true);

            if (isset($data['access_token']) && !empty($data['access_token'])) {
                Cache::put('fedex_auth_token' . $clientId . $clientSecret, $data['access_token'], now()->addMinutes(50));
            }
    
            $status = $response->getStatusCode() === 200 ? HealthCheckLog::STATUS_SUCCESS : HealthCheckLog::STATUS_FAILURE;
    
            $healthCheckLogData = [
                'success' => $response->getStatusCode() === 200 ? true : false,
                'message' => $response->getStatusCode() === 200 ? 'Successfully connected to FedEx API' : 'Failed to authenticate with FedEx API',
                'timestamp' => now()->toDateTimeString(),
            ];
    
            $this->createFedexLog($this->apiEndpoint . 'oauth/token', $status, $healthCheckLogData);
            
            if ($response->getStatusCode() === 200) {
                if (isset($data['access_token']) && !empty($data['access_token'])) {
                    return $data['access_token'];
                }
            }
        } catch (\Exception $e) {
            $this->createFedexLog($this->apiEndpoint . 'oauth/token', HealthCheckLog::STATUS_FAILURE, null, $e->getMessage());

            return null;
        }
    }

    /**
     * Get origin address for shipment.
     *
     * @return array
     */
    protected function getOriginAddress()
    {
        return [
            'contact' => [
                // 'personName' => core()->getConfigData('sales.carriers.fedex.shipper_name'),
                'phoneNumber' => core()->getConfigData('sales.shipping.origin.contact'),
                'companyName' => core()->getConfigData('sales.shipping.origin.store_name'),
                // 'emailAddress' => core()->getConfigData('sales.carriers.fedex.shipper_email')
            ],
            'address' => [
                'streetLines' => [core()->getConfigData('sales.shipping.origin.address')],
                'city' => core()->getConfigData('sales.shipping.origin.city'),
                'stateOrProvinceCode' => core()->getConfigData('sales.shipping.origin.state'),
                'postalCode' => core()->getConfigData('sales.shipping.origin.zipcode'),
                'countryCode' => core()->getConfigData('sales.shipping.origin.country'),
            ]
        ];
    }

    /**
     * Get destination address for shipment.
     *
     * @param  array  $shipmentData
     * @return array
     */
    protected function getDestinationAddress($shipmentData)
    {
        return [
            'contact' => [
                'personName' => $shipmentData['recipient_name'],
                'phoneNumber' => $shipmentData['recipient_phone'],
                'companyName' => $shipmentData['recipient_company'] ?? '',
                'emailAddress' => $shipmentData['recipient_email'] ?? ''
            ],
            'address' => [
                'streetLines' => [$shipmentData['recipient_street']],
                'city' => $shipmentData['recipient_city'],
                'stateOrProvinceCode' => $shipmentData['recipient_state'],
                'postalCode' => $shipmentData['recipient_postal_code'],
                'countryCode' => $shipmentData['recipient_country']
            ]
        ];
    }

    /**
     * Get tracking status from FedEx
     *
     * @param string $trackingNumber
     * @return array|null
     */
    public function getTrackingStatus($trackingNumber, $order)
    {
        try {
            $authToken = $this->getAuthToken($this->trackClientId, $this->trackClientSecret);

            $trackingInfo = [
                'trackingInfo' => [
                    [
                        'trackingNumberInfo' => [
                            'trackingNumber' => $trackingNumber,
                        ]
                    ]
                ],
                "includeDetailedScans" => true
            ];
            
            $response = $this->client->post($this->apiEndpoint . 'track/v1/trackingnumbers', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $authToken,
                    'Content-Type' => 'application/json'
                ],
                'json' => $trackingInfo
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['output']['completeTrackResults'][0]['trackResults'][0]['latestStatusDetail']['statusByLocale'])) {
                return null;
            }

            $latestStatus = $data['output']['completeTrackResults'][0]['trackResults'][0]['latestStatusDetail']['statusByLocale'];

            $lastOrderNote = $order->order_notes()
                ->latest('id')
                ->first();

            $orderStatus = $lastOrderNote ? $lastOrderNote->status : null;

            if ($latestStatus != $orderStatus) {
                $status = $response->getStatusCode() === 200 ? HealthCheckLog::STATUS_SUCCESS : HealthCheckLog::STATUS_FAILURE;

                $healthCheckLogData = [
                    'success' => $response->getStatusCode() === 200 ? true : false,
                    'message' => $response->getStatusCode() === 200 ? 'Successfully fetched tracking status' : 'Failed to fetch tracking status',
                    'timestamp' => now()->toDateTimeString(),
                ];

                $this->createFedexLog($this->apiEndpoint . 'track/v1/trackingnumbers', $status, $healthCheckLogData);
            }

            return [
                'status' => $data['output']['completeTrackResults'][0]['trackResults'][0]['latestStatusDetail']['statusByLocale'],
            ];

        } catch (\Exception $e) {
            $healthCheckLogData = [
                'success' => false,
                'message' => 'Failed to fetch tracking status',
                'timestamp' => now()->toDateTimeString(),
            ];

            $this->createFedexLog($this->apiEndpoint . 'track/v1/trackingnumbers', HealthCheckLog::STATUS_FAILURE, $healthCheckLogData, $e->getMessage());

            Log::error('FedEx tracking status error: ' . $e->getMessage());
            return null;
        }
    }

    public function createFedexLog($endpoint, $status, $data = null, $errorMessage = null)
    {
        if (!$this->healthCheckLogRepository) {
            return;
        }

        if ($status == HealthCheckLog::STATUS_SUCCESS) {
            $this->healthCheckLogRepository->createLog(
                HealthCheckLog::SERVICE_FEDEX,
                $status,
                [
                    'response' => isset($data) && !empty($data) ? json_encode($data) : null,
                    'api_endpoint' => $endpoint
                ]
            );
        } else {
            $this->healthCheckLogRepository->createLog(
                HealthCheckLog::SERVICE_FEDEX,
                $status,
                [
                    'error_message' => isset($errorMessage) && !empty($errorMessage) ? $errorMessage : null,
                    'api_endpoint' => $endpoint
                ]
            );
        }
    }
}
