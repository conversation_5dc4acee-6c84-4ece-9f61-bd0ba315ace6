<?php


namespace Webkul\Stripe\Http\Controllers;


use Webkul\Checkout\Facades\Cart;
use Webkul\Sales\Repositories\OrderRepository;
use Webkul\Sales\Transformers\OrderResource;
use Webkul\Sales\Repositories\InvoiceRepository;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\PaymentIntent;
use Webkul\Admin\Repositories\HealthCheckLogRepository;
use Webkul\Admin\Models\HealthCheckLog;

class StripeController extends Controller
{

  /**
   * OrderRepository $orderRepository
   *
   * @var \Webkul\Sales\Repositories\OrderRepository
   */
  protected $orderRepository;
  /**
   * InvoiceRepository $invoiceRepository
   *
   * @var \Webkul\Sales\Repositories\InvoiceRepository
   */
  protected $invoiceRepository;

  protected $healthCheckLogRepository;

  /**
   * Create a new controller instance.
   *
   * @param  \Webkul\Attribute\Repositories\OrderRepository  $orderRepository
   * @return void
   */
  public function __construct(OrderRepository $orderRepository,  InvoiceRepository $invoiceRepository, HealthCheckLogRepository $healthCheckLogRepository)
  {
    $this->orderRepository = $orderRepository;
    $this->invoiceRepository = $invoiceRepository;
    $this->healthCheckLogRepository = $healthCheckLogRepository;
  }

  /**
   * Redirects to the paytm server.
   *
   * @return \Illuminate\View\View
   */

  public function redirect($locale)
  {
    try {
      session()->put('checkout_data', [
          'is_gift' => request()->input('is_gift', 0),
          'order_comments' => request()->input('order_comments', ''),
          'promotion_emails' => request()->input('promotion_emails', 0),
          'terms_of_service' => request()->input('terms_of_service', 0),
      ]);
      
      $cart = Cart::getCart();
      $billingAddress = $cart->billing_address;
      Stripe::setApiKey(core()->getConfigData('sales.payment_methods.stripe.stripe_secret_key'));

      $shipping_rate = $cart->selected_shipping_rate ? $cart->selected_shipping_rate->price : 0; // shipping rate
      $discount_amount = $cart->discount_amount; // discount amount
      $total_amount =  ($cart->sub_total + $cart->tax_total + $shipping_rate) - $discount_amount; // total amount

      $checkout_session = Session::create([
        'line_items' => [[
          'price_data' => [
            'currency' => $cart->global_currency_code,
            'product_data' => [
              'name' => 'Stripe Checkout Payment order id - ' . $cart->id,
            ],
            'unit_amount' => $total_amount * 100,
          ],
          'quantity' => 1,
        ]],
        'payment_method_types' => [
          'card',
        ],
        'mode' => 'payment',
        'success_url' => route('stripe.success'),
        'cancel_url' => route('stripe.cancel'),
      ]);

      $this->createStripeLog(
        $checkout_session->url,
        HealthCheckLog::STATUS_SUCCESS,
        [
          'success' => true,
          'message' => 'Stripe checkout session created successfully',
          'url' => $checkout_session->url,
          'cart_id' => $cart->id,
          'total_amount' => $total_amount,
          'currency' => core()->getCurrentCurrencyCode(),
          'timestamp' => now()->toDateTimeString(),
        ]
      );

      return redirect()->away($checkout_session->url);
    } catch (\Exception $e) {
      $this->createStripeLog(
        null,
        HealthCheckLog::STATUS_FAILURE,
        [
          'success' => false,
          'message' => 'Stripe checkout session creation failed',
          'timestamp' => now()->toDateTimeString(),
        ],
        $e->getMessage()
      );
    }
  }

  /**
   * success
   */
  public function success($locale)
  { 
    try {
      $cart = Cart::getCart();
      $data = (new OrderResource($cart))->jsonSerialize(); // new class v2.2
      $order = $this->orderRepository->create($data);
      // $order = $this->orderRepository->create(Cart::prepareDataForOrder()); // removed for v2.2
      $this->orderRepository->update(['status' => 'processing'], $order->id);
      if ($order->canInvoice()) {
        $this->invoiceRepository->create($this->prepareInvoiceData($order));
      }

      $this->createStripeLog(
        null,
        HealthCheckLog::STATUS_SUCCESS,
        [
          'success' => true,
          'message' => 'Stripe transaction successful',
          'order_id' => $order->id,
          'cart_id' => $cart->id,
          'currency' => $order->order_currency_code,
          'total_amount' => $cart->grand_total,
          'timestamp' => now()->toDateTimeString(),
        ]
      );

      Cart::deActivateCart();
      session()->put('order_id', $order->id); // line instead of $order in v2.1

      session()->forget('checkout_data');

      // Order and prepare invoice
      return redirect()->route('shop.checkout.onepage.success'); 
    } catch (\Exception $e) {
      $this->createStripeLog(
        null,
        HealthCheckLog::STATUS_FAILURE,
        [
          'success' => false,
          'message' => 'Stripe transaction failed',
          'timestamp' => now()->toDateTimeString(),
        ],
        $e->getMessage()
      );
    }
  }

  public function createPaymentCheckoutSession()
  {
    try {
      $cart = Cart::getCart();

      if (!$cart) {
        return response()->json(['error' => 'Cart not found'], 404);
      }

      Stripe::setApiKey(core()->getConfigData('sales.payment_methods.stripe.stripe_secret_key'));

      $billingAddress = $cart->billing_address;
      $shippingAddress = $cart->shipping_address;

      $paymentIntentData = [
        'amount' => (int) round($cart->grand_total * 100),
        'currency' => strtolower($cart->cart_currency_code ?? $cart->base_currency_code),
        'automatic_payment_methods' => [
          'enabled' => true,
        ],
        'metadata' => [
          'cart_id' => $cart->id,
          'customer_email' => $billingAddress ? $billingAddress->email : '',
          'order_description' => 'Order from ' . core()->getCurrentChannel()->name,
        ],
      ];

      if ($billingAddress && $billingAddress->email) {
        $paymentIntentData['receipt_email'] = $billingAddress->email;
      }

      if ($shippingAddress) {
        $paymentIntentData['shipping'] = [
          'name' => $shippingAddress->first_name . ' ' . $shippingAddress->last_name,
          'address' => [
            'line1' => $shippingAddress->address[0] ?? '',
            'line2' => $shippingAddress->address[1] ?? null,
            'city' => $shippingAddress->city,
            'state' => $shippingAddress->state,
            'postal_code' => $shippingAddress->postcode,
            'country' => $shippingAddress->country,
          ],
        ];
      }

      $paymentIntent = PaymentIntent::create($paymentIntentData);

      $this->createStripeLog(
        'create_payment_intent',
        HealthCheckLog::STATUS_SUCCESS,
        [
          'success' => true,
          'message' => 'Payment intent created successfully',
          'payment_intent_id' => $paymentIntent->id,
          'cart_id' => $cart->id,
          'amount' => $paymentIntentData['amount'],
          'currency' => $paymentIntentData['currency'],
          'timestamp' => now()->toDateTimeString(),
        ]
      );

      return response()->json([
        'clientSecret' => $paymentIntent->client_secret,
        'paymentIntentId' => $paymentIntent->id
      ]);

    } catch (\Exception $e) {
      $this->createStripeLog(
        'create_payment_intent',
        HealthCheckLog::STATUS_FAILURE,
        [
          'success' => false,
          'message' => 'Payment intent creation failed',
          'cart_id' => $cart->id ?? null,
          'timestamp' => now()->toDateTimeString(),
        ],
        $e->getMessage()
      );

      return response()->json([
        'error' => 'Failed to initialize payment',
        'message' => $e->getMessage()
      ], 500);
    }
  }

  /**
   * failure
   */
  public function failure()
  {
    $errMsg = 'Stripe payment either cancelled or transaction failure.';

    session()->flash('error', $errMsg);

    $this->createStripeLog(
      null,
      HealthCheckLog::STATUS_FAILURE,
      [
        'success' => false,
        'message' => 'Stripe transaction failed',
        'timestamp' => now()->toDateTimeString(),
      ],
      $errMsg
    );

    return redirect()->route('shop.checkout.cart.index');
  }

  /**
   * Prepares order's invoice data for creation.
   *
   * @return array
   */
  protected function prepareInvoiceData($order)
  {
    $invoiceData = ["order_id" => $order->id,];

    foreach ($order->items as $item) {
      $invoiceData['invoice']['items'][$item->id] = $item->qty_to_invoice;
    }

    return $invoiceData;
  }

  protected function createStripeLog($endpoint = null, $status, $data = null, $errorMessage = null)
  {
    if (!$this->healthCheckLogRepository) {
        return;
    }

    if ($status == HealthCheckLog::STATUS_SUCCESS) {
      $this->healthCheckLogRepository->createLog(
        HealthCheckLog::SERVICE_STRIPE,
        $status,
        [
          'response' => isset($data) && !empty($data) ? json_encode($data) : null,
          'api_endpoint' => $endpoint
        ]
      );
    } else {
      $this->healthCheckLogRepository->createLog(
        HealthCheckLog::SERVICE_STRIPE,
        $status,
        [
          'response' => isset($data) && !empty($data) ? json_encode($data) : null,
          'error_message' => isset($errorMessage) && !empty($errorMessage) ? $errorMessage : null,
          'api_endpoint' => $endpoint
        ]
      );
    }
  }

  public function logTransaction($locale)
  {
    $data = request()->all();

    $this->createStripeLog(
      null,
      HealthCheckLog::STATUS_FAILURE,
      [
        'success' => false,
        'message' => 'Stripe transaction failed',
        'cart_id' => Cart::getCart()->id,
        'total_amount' => $data['total_amount'],
        'timestamp' => now()->toDateTimeString(),
      ],
      $data['message']
    );
  }

  public function storeCheckoutData($locale)
  {
    try {
      $data = request()->all();

      session()->put('checkout_data', [
        'is_gift' => $data['is_gift'] ?? 0,
        'order_comments' => $data['order_comments'] ?? '',
        'promotion_emails' => $data['promotion_emails'] ?? 0,
        'terms_of_service' => $data['terms_of_service'] ?? 0,
      ]);

      return response()->json(['success' => true]);
    } catch (\Exception $e) {
      return response()->json(['error' => 'Failed to store checkout data'], 500);
    }
  }
}