<?php

namespace Webkul\Shipping\Carriers;

use Webkul\Checkout\Facades\Cart;
use Webkul\Checkout\Models\CartShippingRate;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Webkul\Admin\Repositories\HealthCheckLogRepository;
use Webkul\Admin\Models\HealthCheckLog;
use Illuminate\Support\Facades\Cache;

class FedEx extends AbstractShipping
{
    /**
     * Code.
     *
     * @var string
     */
    protected $code  = 'fedex';

    /**
     * Shipping method code.
     *
     * @var string
     */
    protected $method = 'fedex_fedex';

    public $client;

    public $apiEndpoint;
    protected $healthCheckLogRepository;

    public function __construct()
    {
        $this->client = new Client();
        $this->healthCheckLogRepository = app(HealthCheckLogRepository::class);
        $this->apiEndpoint = core()->getConfigData('sales.carriers.fedex.base_url');
    }

    /**
     * Calculate rate for fedex
     */
    public function calculate()
    {
        if (!$this->isAvailable()) {
            // return $this->getFallbackRate(); 
            return false;
        }

        try {
            $cart = Cart::getCart();

            if (!$cart) {
                return false;
            }

            $countryCode = $cart->shipping_address->country;

            // For GCC countries, use FedEx live rates
            if (in_array($countryCode, ['SA', 'KW', 'BH', 'QA', 'OM'])) {
                return $this->getGCCRate($cart);
            }

            // For all other countries, use standard FedEx rates with safety margin
            $rates = $this->getFedExRates();

            if (! $rates) {
                return false;
            }

            $lowestRate = $this->getLowestRate($rates);
            
            if (! $lowestRate) {
                return false;
            }

            // Apply safety margin (10%)
            // $safetyMargin = $this->getConfigData('safety_margin') ?: 10;
            // $finalAmount = $lowestRate['amount'] * (1 + ($safetyMargin / 100));

            $finalAmount = core()->convertPrice($lowestRate['amount']);

            $shippingRate = new CartShippingRate;
            $shippingRate->carrier = 'fedex';
            $shippingRate->carrier_title = $this->getConfigData('title');
            $shippingRate->method = 'fedex_fedex';
            $shippingRate->method_title = $lowestRate['serviceName'];
            $shippingRate->method_description = $this->getConfigData('description');
            $shippingRate->price = $finalAmount;
            $shippingRate->base_price = $finalAmount;

            return $shippingRate;
        } catch (\Exception $e) {
            Log::error('FedEx shipping calculation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get UAE specific rates
     */
    protected function getUAERate($cart)
    {
        $cartTotal = $cart ? $cart->base_sub_total : 0;
    
        // Free shipping for orders above AED 600
        if (core()->convertPrice($cartTotal, 'AED') >= 600) {
            $basePrice = 0;
            $methodTitle = 'Free Shipping';
        } else {
            // AED 25 flat rate for UAE orders
            $basePrice = core()->convertPrice(core()->getConfigData('sales.carriers.flatrate.default_rate'));
            $methodTitle = 'Flat Rate Shipping';
        }

        // Convert price to current currency
        $price = core()->convertPrice($basePrice);

        $shippingRate = new CartShippingRate;
        $shippingRate->carrier = 'fedex';
        $shippingRate->carrier_title = $this->getConfigData('title') ?? 'FedEx';
        $shippingRate->method = 'fedex_fedex';
        $shippingRate->method_title = $methodTitle;
        $shippingRate->method_description = $this->getConfigData('description') ?? 'FedEx Shipping';
        $shippingRate->price = $price;
        $shippingRate->base_price = $price;

        return $shippingRate;
    }

    /**
     * Get GCC specific rates with dimensional weight and remote zone logic
     */
    protected function getGCCRate($cart)
    {
        // Get FedEx live rates
        $rates = $this->getFedExRates();
        
        if (! $rates) {
            return $this->getFallbackRate();
        }

        $lowestRate = $this->getLowestRate($rates);
        
        if (! $lowestRate) {
            return $this->getFallbackRate();
        }

        // Apply safety margin (10%)
        // $safetyMargin = $this->getConfigData('safety_margin') ?: 10;
        // $finalAmount = $lowestRate['amount'] * (1 + ($safetyMargin / 100));

        $finalAmount = core()->convertPrice($lowestRate['amount']);

        $shippingRate = new CartShippingRate;
        $shippingRate->carrier = 'fedex';
        $shippingRate->carrier_title = $this->getConfigData('title');
        $shippingRate->method = 'fedex_fedex';
        $shippingRate->method_title = $lowestRate['serviceName'] . ' (GCC)';
        $shippingRate->method_description = $this->getConfigData('description');
        $shippingRate->price = $finalAmount;
        $shippingRate->base_price = $finalAmount;

        return $shippingRate;
    }

    /**
     * Check if carrier is available
     */
    public function isAvailable(): bool
    {
        $active = $this->getConfigData('active');
        $apiKey = $this->getConfigData('ship_api_key');
        $apiSecret = $this->getConfigData('ship_api_secret');

        if (empty($active)) {
            return false;
        }

        if (empty($apiKey) || empty($apiSecret)) {
            return false;
        }

        $cart = Cart::getCart();
        if (!$cart || !$cart->shipping_address) {
            return true; 
        }

        $countryCode = $cart->shipping_address->country;
        $cartTotal = $cart->base_sub_total;
        
        // For UAE, don't show FedEx if cart total >= 600 AED (free shipping applies)
        // or if cart total < 600 AED (flat rate applies)
        if ($countryCode == 'AE') {
            return false;
        }
        
        // For GCC countries, show FedEx
        if (in_array($countryCode, ['SA', 'KW', 'BH', 'QA', 'OM'])) {
            return true;
        }

        return true;
    }

    /**
     * Get configuration data
     */
    public function getConfigData($field)
    {
        $value = core()->getConfigData('sales.carriers.fedex.' . $field);
        
        return $value;
    }

    protected function getFedExRates()
    {
        $cart = Cart::getCart();
        
        try {
            $cacheKey = 'fedex_rates_' . md5(json_encode([
                'postcode' => $cart->shipping_address->postcode ?? '',
                'country' => $cart->shipping_address->country ?? '',
                'items' => $this->getPackageItems($cart) ?? []
            ]));

            // Try to get rates from cache first
            $cachedRates = Cache::get($cacheKey);
            if ($cachedRates) {
                return $cachedRates;
            }

            // Get authentication token
            $token = $this->getAuthToken($this->getConfigData('ship_api_key'), $this->getConfigData('ship_api_secret'));
            
            if (!$token) {
                return false;
            }

            // Prepare rate request
            $rateRequest = [
                'accountNumber' => [
                    'value' => $this->getConfigData('account_number')
                ],
                'requestedShipment' => [
                    'shipper' => [
                        'address' => $this->getOriginAddress()
                    ],
                    'recipient' => [
                        'address' => $this->getDestinationAddress($cart)
                    ],
                    'pickupType' => 'DROPOFF_AT_FEDEX_LOCATION',
                    'rateRequestType' => ['LIST', 'ACCOUNT'],
                    'requestedPackageLineItems' => $this->getPackageItems($cart)
                ]
            ];

            // Make API call to get rates
            $response = $this->client->post($this->apiEndpoint . 'rate/v1/rates/quotes', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'json' => $rateRequest
            ]);

            $result = json_decode($response->getBody(), true);

            if (isset($result['output']['rateReplyDetails']) && !empty($result['output']['rateReplyDetails'])) {
                Cache::put($cacheKey, $result, now()->addMinutes(5));
            }

            $status = isset($result['output']['rateReplyDetails']) && !empty($result['output']['rateReplyDetails']) ? HealthCheckLog::STATUS_SUCCESS : HealthCheckLog::STATUS_FAILURE;
            $response = [
                'success' => isset($result['output']['rateReplyDetails']) && !empty($result['output']['rateReplyDetails']) ? true : false,
                'message' => isset($result['output']['rateReplyDetails']) && !empty($result['output']['rateReplyDetails']) ? 'Successfully fetched rates' : 'Failed to fetch rates',
                'timestamp' => now()->toDateTimeString(),
            ];

            $this->createFedexLog($this->apiEndpoint . 'rate/v1/rates/quotes', $status, $response);

            return $result;
        } catch (\Exception $e) {
            $this->createFedexLog($this->apiEndpoint . 'rate/v1/rates/quotes', HealthCheckLog::STATUS_FAILURE, null, $e->getMessage());

            return false;
        }
    }

    public function getAuthToken($clientId, $clientSecret)
    {
        try {
            $token = Cache::get('fedex_auth_token' . $clientId . $clientSecret);
            
            if ($token) {
                return $token;
            }

            $response = $this->client->post($this->apiEndpoint . 'oauth/token', [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret
                ]
            ]);
    
            $data = json_decode($response->getBody(), true);

            if (isset($data['access_token']) && !empty($data['access_token'])) {
                Cache::put('fedex_auth_token' . $clientId . $clientSecret, $data['access_token'], now()->addMinutes(50));
            }

            $status = isset($data['access_token']) && !empty($data['access_token']) ? HealthCheckLog::STATUS_SUCCESS : HealthCheckLog::STATUS_FAILURE;

            $response = [
                'success' => isset($data['access_token']) && !empty($data['access_token']) ? true : false,
                'message' => isset($data['access_token']) && !empty($data['access_token']) ? 'Successfully connected to FedEx API' : 'Failed to authenticate with FedEx API',
                'timestamp' => now()->toDateTimeString(),
            ];

            $this->createFedexLog($this->apiEndpoint . 'oauth/token', $status, $response);
            
            return $data['access_token'] ?? null;
        } catch (\Exception $e) {
            $this->createFedexLog($this->apiEndpoint . 'oauth/token', HealthCheckLog::STATUS_FAILURE, null, $e->getMessage());

            return null;
        }
    }

    protected function getOriginAddress()
    {
        $postalCode = core()->getConfigData('sales.shipping.origin.zipcode');
        $country = core()->getConfigData('sales.shipping.origin.country');
        
        return [
            'postalCode' => $postalCode,
            'countryCode' => $country,
        ];
    }

    protected function getDestinationAddress($cart)
    {
        $address = $cart->shipping_address;
        
        return [
            'postalCode' => $address->postcode,
            'countryCode' => $address->country,
        ];
    }

    protected function getPackageItems($cart)
    {
        $items = [];
        $totalWeight = 0;
        // $maxDimensions = [
        //     'length' => 0,
        //     'width' => 0,
        //     'height' => 0
        // ];
        
        foreach ($cart->items as $item) {
            if ($item->getTypeInstance()->isStockable()) {
                $weight = $item->product->weight ?: 1;
                $totalWeight += ($weight * $item->quantity);
                
                // Get largest dimensions
                // $maxDimensions['length'] = max($maxDimensions['length'], $item->product->length ?: 1);
                // $maxDimensions['width'] = max($maxDimensions['width'], $item->product->width ?: 1);
                // $maxDimensions['height'] = max($maxDimensions['height'], $item->product->height ?: 1);
            }
        }
        
        // Create single package with total weight and largest dimensions
        $items[] = [
            'weight' => [
                'units' => 'KG',
                'value' => $totalWeight
            ],
            // 'dimensions' => [
            //     'length' => $maxDimensions['length'],
            //     'width' => $maxDimensions['width'],
            //     'height' => $maxDimensions['height'],
            //     'units' => 'IN'
            // ],
            // 'groupPackageCount' => 1
        ];

        return $items;
    }

    protected function getLowestRate($rates)
    {
        if (empty($rates['output']['rateReplyDetails'])) {
            return false;
        }

        $available = array_filter($rates['output']['rateReplyDetails'], function($rate) {
            return isset($rate['ratedShipmentDetails'][0]['totalNetFedExCharge']);
        });

        if (empty($available)) {
            return false;
        }

        usort($available, function($a, $b) {
            return $a['ratedShipmentDetails'][0]['totalNetFedExCharge'] - $b['ratedShipmentDetails'][0]['totalNetFedExCharge'];
        });

        $lowest = reset($available);

        return [
            'serviceName' => $lowest['serviceName'],
            'amount' => $lowest['ratedShipmentDetails'][0]['totalNetFedExCharge']
        ];
    }

    protected function getFallbackRate()
    {
        $cart = Cart::getCart();
        $cartTotal = $cart ? $cart->base_sub_total : 0;
        $shippingAddress = $cart ? $cart->shipping_address : null;
        $countryCode = $shippingAddress ? $shippingAddress->country : null;

        // UAE specific rates
        if ($countryCode == 'AE') {
            // Free shipping for orders above AED 600
            if (core()->convertPrice($cartTotal, 'AED') >= 600) {
                $basePrice = 0;
                $methodTitle = 'Free Shipping';
            } else {
                // AED 25 flat rate for UAE orders
                $basePrice = core()->convertPrice(core()->getConfigData('sales.carriers.flatrate.default_rate'));
                $methodTitle = 'Flat Rate Shipping';
            }
        } else {
            // For non-UAE countries, use 10% of cart total as fallback
            $basePrice = $cartTotal * 0.1; 
            $methodTitle = 'FedEx International';
        }

        $price = core()->convertPrice($basePrice);

        $shippingRate = new CartShippingRate;
        $shippingRate->carrier = 'fedex';
        $shippingRate->carrier_title = $this->getConfigData('title') ?? 'FedEx';
        $shippingRate->method = 'fedex_fedex';
        // $shippingRate->method_title = 'FedEx Ground';
        $shippingRate->method_title = $methodTitle;
        $shippingRate->method_description = $this->getConfigData('description') ?? 'FedEx Shipping';
        $shippingRate->price = $price;
        $shippingRate->base_price = $price;

        return $shippingRate;
    }

    public function createFedexLog($endpoint, $status, $data = null, $errorMessage = null)
    {
        if (!$this->healthCheckLogRepository) {
            return;
        }

        if ($status == HealthCheckLog::STATUS_SUCCESS) {
            $this->healthCheckLogRepository->createLog(
                HealthCheckLog::SERVICE_FEDEX,
                $status,
                [
                    'response' => isset($data) && !empty($data) ? json_encode($data) : null,
                    'api_endpoint' => $endpoint
                ]
            );
        } else {
            $this->healthCheckLogRepository->createLog(
                HealthCheckLog::SERVICE_FEDEX,
                $status,
                [
                    'error_message' => isset($errorMessage) && !empty($errorMessage) ? $errorMessage : null,
                    'api_endpoint' => $endpoint
                ]
            );
        }
    }
}
