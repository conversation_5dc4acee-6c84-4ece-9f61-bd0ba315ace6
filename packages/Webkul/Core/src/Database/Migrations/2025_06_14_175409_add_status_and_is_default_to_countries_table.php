<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            $table->boolean('status')->default(false)->after('name');
            $table->integer('currency_id')->unsigned()->nullable()->after('status');
            $table->integer('locale_id')->unsigned()->nullable()->after('currency_id');
            $table->foreign('currency_id')->references('id')->on('currencies')->onDelete('cascade');
            $table->foreign('locale_id')->references('id')->on('locales')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropForeign(['currency_id']);
            $table->dropForeign(['locale_id']);
            $table->dropColumn('currency_id');
            $table->dropColumn('locale_id');
        });
    }
};
