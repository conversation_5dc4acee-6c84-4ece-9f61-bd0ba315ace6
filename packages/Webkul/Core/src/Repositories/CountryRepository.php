<?php

namespace Webkul\Core\Repositories;

use Webkul\Core\Eloquent\Repository;
use Webkul\Core\Models\Country;

class CountryRepository extends Repository
{
    /**
     * Specify Model class name
     */
    public function model(): string
    {
        return Country::class;
    }

    public function getActiveCountries()
    {
        return $this->model->where('status', 1)->get();
    }

    public function getActiveCountriesWithStates()
    {
        return $this->model->where('status', 1)->with('states')->get();
    }
}
